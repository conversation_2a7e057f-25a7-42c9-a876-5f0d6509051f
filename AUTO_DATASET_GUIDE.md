# 🤖 Automatic Dataset Preparation Guide

Transform raw images into FLUX-ready training datasets with AI-powered captioning and intelligent processing.

## 🚀 Quick Start

### One-Command Dataset Preparation
```bash
# Just provide images - everything else is automatic!
./scripts/prepare_flux_auto.sh /path/to/your/images ./data/train
```

That's it! The system will:
- ✅ Analyze image quality and properties
- ✅ Generate descriptive captions using AI
- ✅ Create organized dataset structure
- ✅ Save FLUX-ready metadata
- ✅ Handle any image size or format

## 🎨 Features

### Automatic Captioning Models
- **BLIP2** (Recommended): High-quality captions with good detail
- **BLIP**: Faster processing, good for large datasets
- **CLIP**: Basic descriptions, very fast
- **InstructBLIP**: Advanced guided captioning

### Intelligent Processing
- **Quality Analysis**: Automatic filtering of low-quality images
- **Resolution Handling**: Smart resizing while preserving aspect ratios
- **Format Support**: JPG, PNG, WEBP, BMP, TIFF
- **Batch Processing**: Efficient multi-threaded processing
- **Memory Optimization**: Handles large datasets without memory issues

### Caption Enhancement
- **Style Detection**: Identifies artistic styles and techniques
- **Content Analysis**: Describes objects, scenes, and composition
- **Quality Descriptors**: Adds appropriate quality terms
- **Aspect Ratio Awareness**: Mentions orientation and framing

## 📋 Usage Examples

### Basic Usage
```bash
# Simple automatic preparation
./scripts/prepare_flux_auto.sh /path/to/images ./data/train
```

### High-Quality Dataset
```bash
# Strict quality filtering for professional results
./scripts/prepare_flux_auto.sh /path/to/images ./data/train \
  --captioning_model blip2 \
  --min_resolution 768 \
  --quality_threshold 0.8 \
  --enhance_captions
```

### Large Dataset Processing
```bash
# Optimized for processing thousands of images
./scripts/prepare_flux_auto.sh /path/to/images ./data/train \
  --max_images 10000 \
  --batch_size 8 \
  --num_workers 8 \
  --captioning_model blip
```

### Memory-Efficient Processing
```bash
# For systems with limited GPU memory
./scripts/prepare_flux_auto.sh /path/to/images ./data/train \
  --batch_size 1 \
  --captioning_model blip \
  --no_enhance
```

### Reference-Only Dataset
```bash
# Create metadata without copying images
./scripts/prepare_flux_auto.sh /path/to/images ./data/train \
  --no_copy \
  --captioning_model blip2
```

## 🔧 Configuration Options

### Captioning Models
| Model | Quality | Speed | Memory | Best For |
|-------|---------|-------|--------|----------|
| **blip2** | Excellent | Moderate | High | Best quality captions |
| **blip** | Good | Fast | Moderate | Balanced performance |
| **clip** | Basic | Very Fast | Low | Quick processing |
| **instructblip** | Excellent | Slow | Very High | Guided captioning |

### Quality Settings
```bash
--min_resolution 512      # Minimum image size (default: 512)
--max_resolution 2048     # Maximum image size (default: 2048)
--quality_threshold 0.7   # Quality score 0-1 (default: 0.7)
```

### Performance Settings
```bash
--batch_size 4           # Images per batch (default: 4)
--num_workers 4          # Processing threads (default: 4)
--max_images 1000        # Limit number of images
```

### Processing Options
```bash
--enhance_captions       # Add style and quality descriptors
--no_copy               # Don't copy images, use references
--cache_dir /path       # Model cache directory
```

## 📊 Output Structure

The automatic preparation creates a complete FLUX-ready dataset:

```
data/train/
├── metadata.json           # FLUX-compatible metadata
├── dataset_stats.json      # Processing statistics
├── images/                 # Processed images (if --copy enabled)
│   ├── image_000001.jpg
│   ├── image_000002.jpg
│   └── ...
└── logs/                   # Processing logs
```

### Metadata Format
```json
[
  {
    "image": "images/image_000001.jpg",
    "caption": "a high-quality photograph of a serene landscape with mountains and a lake, beautiful lighting, professional composition",
    "width": 1024,
    "height": 768,
    "aspect_ratio": 1.33,
    "quality_score": 0.85,
    "original_path": "/original/path/photo1.jpg"
  }
]
```

## 🎯 Caption Review and Editing

### Interactive Caption Editor
```bash
# Launch GUI for caption review and editing
python scripts/review_captions.py --dataset_dir ./data/train
```

Features:
- ✅ **Visual Review**: See image and caption side-by-side
- ✅ **Easy Editing**: Click to edit captions
- ✅ **Navigation**: Arrow keys to browse images
- ✅ **Suggestions**: Built-in caption improvement suggestions
- ✅ **Batch Operations**: Apply changes to multiple images
- ✅ **Backup**: Automatic backup of original captions

### Keyboard Shortcuts
- `←/→`: Navigate between images
- `Ctrl+S`: Save changes
- `Enter`: Apply current caption
- `Esc`: Reset to original caption

## 🚀 Integration with Training

### Direct Training
```bash
# Prepare dataset and start training
./scripts/prepare_flux_auto.sh /path/to/images ./data/train
./scripts/train_production.sh flux config/flux_variable_size_config.yaml --variable_size
```

### Variable-Size Training
The automatic preparation is optimized for variable-size training:
- Preserves original aspect ratios
- Creates appropriate bucket assignments
- Maintains image quality without cropping

## 📈 Performance Optimization

### GPU Memory Recommendations
| GPU Memory | Batch Size | Model | Notes |
|------------|------------|-------|-------|
| 4-8GB | 1 | blip | Basic processing |
| 8-16GB | 2-4 | blip/blip2 | Good performance |
| 16-24GB | 4-8 | blip2 | Optimal quality |
| 24GB+ | 8+ | blip2/instructblip | Maximum performance |

### Processing Speed Tips
1. **Use SSD storage** for faster image loading
2. **Increase num_workers** for CPU-bound operations
3. **Use appropriate batch_size** for your GPU
4. **Choose faster models** for large datasets
5. **Enable GPU** for captioning models

## 🔍 Quality Control

### Automatic Quality Metrics
- **Resolution Score**: Based on minimum dimension
- **Aspect Ratio Score**: Penalizes extreme ratios
- **Content Score**: Based on captioning confidence
- **Overall Quality**: Combined score for filtering

### Manual Quality Review
```bash
# Review low-quality images
python scripts/review_quality.py --dataset_dir ./data/train --threshold 0.5
```

## 🛠️ Troubleshooting

### Common Issues

#### No Captioning Models Available
```bash
# Install required dependencies
pip install transformers torch torchvision
pip install clip-by-openai  # Optional for CLIP
```

#### CUDA Out of Memory
```bash
# Reduce batch size
./scripts/prepare_flux_auto.sh /path/to/images ./data/train --batch_size 1

# Use CPU-only model
./scripts/prepare_flux_auto.sh /path/to/images ./data/train --device cpu
```

#### Slow Processing
```bash
# Use faster model
./scripts/prepare_flux_auto.sh /path/to/images ./data/train --captioning_model blip

# Increase workers
./scripts/prepare_flux_auto.sh /path/to/images ./data/train --num_workers 8
```

#### Poor Caption Quality
```bash
# Use better model
./scripts/prepare_flux_auto.sh /path/to/images ./data/train --captioning_model blip2

# Enable enhancement
./scripts/prepare_flux_auto.sh /path/to/images ./data/train --enhance_captions
```

## 🎨 Advanced Usage

### Custom Caption Templates
```python
# Create custom captioning script
from src.auto_caption import AutoCaptioner

captioner = AutoCaptioner(model_name="blip2")

# Custom prompts for specific styles
art_prompt = "Describe this artwork in detail, including style and technique"
photo_prompt = "Describe this photograph, including lighting and composition"

caption = captioner.caption_image(image, prompt=art_prompt)
```

### Batch Processing with Custom Logic
```python
from src.auto_dataset_prep import AutoDatasetPreparator

# Custom preparator with specific settings
preparator = AutoDatasetPreparator(
    input_dir="/path/to/images",
    output_dir="./data/train",
    captioning_model="blip2",
    quality_threshold=0.8,
    enhance_captions=True
)

# Run with custom filtering
stats = preparator.prepare_dataset()
```

## 📚 Examples and Demos

### Try the Interactive Demo
```bash
# See automatic captioning in action
python examples/auto_dataset_demo.py
```

### Real-World Examples
- **Art Collection**: High-quality art images with style descriptions
- **Photography**: Professional photos with technical details
- **Product Images**: E-commerce photos with descriptive captions
- **Social Media**: Mixed content with engaging descriptions

## 🎯 Best Practices

### Dataset Preparation
1. **Organize by theme** for better training results
2. **Remove duplicates** to avoid overfitting
3. **Balance aspect ratios** for diverse training
4. **Check caption quality** before training
5. **Use consistent naming** for organization

### Caption Quality
1. **Review generated captions** for accuracy
2. **Add specific details** relevant to your use case
3. **Use consistent terminology** across dataset
4. **Include style descriptors** for artistic content
5. **Mention technical aspects** for photography

### Training Integration
1. **Use variable-size training** for best results
2. **Start with small datasets** to test setup
3. **Monitor training metrics** for quality
4. **Validate with held-out images** regularly
5. **Iterate on caption quality** based on results

## 🎉 Success Stories

The automatic dataset preparation has been successfully used for:
- **Artistic Style Transfer**: Training FLUX on specific art styles
- **Product Photography**: E-commerce image generation
- **Architectural Visualization**: Building and interior design
- **Character Design**: Consistent character generation
- **Brand Assets**: Company-specific visual content

## 🔗 Integration with Other Tools

### Works Seamlessly With
- **Variable-Size Training**: Preserves aspect ratios automatically
- **Production Training**: Full monitoring and error handling
- **FLUX Models**: Optimized metadata format
- **Weights & Biases**: Automatic experiment tracking
- **Cloud Storage**: S3, GCS, Azure Blob support

Ready to transform your images into a professional training dataset? Start with:
```bash
./scripts/prepare_flux_auto.sh /your/images ./data/train
```

🎨 **From raw images to trained model in minutes, not hours!**
