# 🎨 Unified Diffusion Model Training

A clean, production-ready system for training both Wan2.1 and FLUX models with automatic dataset preparation and variable-size support. Optimized for A40 GPUs.

## 🚀 Quick Start

### 1. Setup
```bash
git clone <repository>
cd finetune
pip install -r requirements.txt

# Setup environment
cp .env.example .env
# Edit .env with your HF_TOKEN
```

### 2. Train FLUX (Text-to-Image)
```bash
# Automatic dataset preparation + training
python train.py --model flux --input_dir /path/to/images --prepare_dataset

# Or with existing dataset
python train.py --model flux --data_dir ./data/train
```

### 3. Train Wan2.1 (Image-to-Video)
```bash
# With prepared dataset
python train.py --model wan2.1 --data_dir ./data/train
```

### 4. Generate Content
```bash
# FLUX image generation
python inference.py --model flux --prompt "a beautiful landscape" --lora_path ./outputs/lora

# Wan2.1 video generation
python inference.py --model wan2.1 --image_path input.jpg --prompt "flowing water" --lora_path ./outputs/lora
```

## 📁 Core Files

### Training
- **`train.py`** - Main training entry point
- **`src/train_unified.py`** - Unified training logic
- **`config/unified_config.yaml`** - A40-optimized configuration

### Dataset Preparation
- **`src/dataset_auto.py`** - Automatic dataset preparation with AI captioning

### Inference
- **`inference.py`** - Unified inference for both models

### Configuration
- **`src/config_manager.py`** - Environment and config management
- **`src/auth_manager.py`** - Authentication handling

## 🔧 Features

### Automatic Dataset Preparation
- **AI Captioning**: BLIP2/BLIP models for automatic caption generation
- **Quality Analysis**: Automatic filtering and quality scoring
- **Format Support**: JPG, PNG, WEBP, BMP, TIFF
- **Batch Processing**: Optimized for A40 GPU

### Variable-Size Training
- **No Cropping**: Preserves full image content
- **Aspect Ratio Preservation**: Maintains original proportions
- **Intelligent Bucketing**: Groups similar sizes for efficiency
- **Memory Optimization**: Reduces waste by 40-60%

### Production Features
- **Environment Management**: Secure .env configuration
- **Authentication**: Automatic HF and W&B token handling
- **Monitoring**: System resources and training metrics
- **Error Handling**: CUDA OOM recovery and checkpoint safety

### A40 Optimizations
- **Large Batch Training**: Utilizes 48GB VRAM efficiently
- **Mixed Precision**: BF16 support for faster training
- **Memory Efficient Attention**: XFormers integration
- **Optimal Batch Sizes**: Automatically configured per model

## ⚙️ Configuration

### Environment Variables (.env)
```bash
# Required
HF_TOKEN=your_huggingface_token

# Optional
WANDB_API_KEY=your_wandb_key
WANDB_PROJECT=your_project_name
OUTPUT_DIR=./outputs
CACHE_DIR=./cache
```

### Training Parameters
```bash
# Basic training
python train.py --model flux --data_dir ./data/train

# Custom batch size (A40 can handle large batches)
python train.py --model flux --data_dir ./data/train --batch_size 8

# Mixed precision optimization
python train.py --model flux --data_dir ./data/train --mixed_precision bf16

# Resume from checkpoint
python train.py --model flux --data_dir ./data/train --resume_from_checkpoint ./outputs/checkpoint-1000
```

### Dataset Preparation Options
```bash
# High-quality dataset
python train.py --model flux --input_dir /path/to/images --prepare_dataset \
  --captioning_model blip2 --min_resolution 768 --quality_threshold 0.8

# Fast processing
python train.py --model flux --input_dir /path/to/images --prepare_dataset \
  --captioning_model blip --max_resolution 1024

# Memory efficient
python train.py --model flux --input_dir /path/to/images --prepare_dataset \
  --captioning_model blip --batch_size 4
```

## 🎯 Model-Specific Usage

### FLUX (Text-to-Image)
```bash
# Training
python train.py --model flux --input_dir /path/to/images --prepare_dataset

# Inference
python inference.py --model flux \
  --prompt "a serene mountain landscape" \
  --width 1024 --height 1024 \
  --lora_path ./outputs/lora
```

### Wan2.1 (Image-to-Video)
```bash
# Training (requires prepared video dataset)
python train.py --model wan2.1 --data_dir ./data/train

# Inference
python inference.py --model wan2.1 \
  --image_path input.jpg \
  --prompt "gentle waves on the shore" \
  --num_frames 81 \
  --lora_path ./outputs/lora
```

## 📊 A40 Performance

### Recommended Settings
| Model | Resolution | Batch Size | Memory Usage | Training Speed |
|-------|------------|------------|--------------|----------------|
| FLUX | 512x512 | 8 | ~20GB | Fast |
| FLUX | 1024x1024 | 4 | ~35GB | Optimal |
| Wan2.1 | 720p | 2 | ~40GB | Good |
| Wan2.1 | 1080p | 1 | ~45GB | Memory Limit |

### Memory Optimization
- **Gradient Checkpointing**: Enabled by default
- **Mixed Precision**: BF16 for A40
- **XFormers**: Memory efficient attention
- **Variable Batch Size**: Automatic adjustment

## 🔍 Monitoring

### Training Metrics
- Loss tracking and visualization
- Learning rate scheduling
- GPU memory usage
- Training speed (steps/sec)

### Weights & Biases Integration
```bash
# Enable W&B logging
export WANDB_API_KEY=your_key
python train.py --model flux --data_dir ./data/train
```

### System Monitoring
- Real-time GPU utilization
- Memory usage tracking
- Temperature monitoring
- Error detection and recovery

## 🛠️ Troubleshooting

### Common Issues

#### CUDA Out of Memory
```bash
# Reduce batch size
python train.py --model flux --data_dir ./data/train --batch_size 2

# Enable gradient checkpointing
python train.py --model flux --data_dir ./data/train --gradient_accumulation_steps 8
```

#### Model Access Issues
```bash
# Check HF token
export HF_TOKEN=your_token
huggingface-cli login

# Verify model access
python -c "from transformers import AutoTokenizer; AutoTokenizer.from_pretrained('black-forest-labs/FLUX.1-dev')"
```

#### Slow Dataset Preparation
```bash
# Use faster captioning model
python train.py --model flux --input_dir /path/to/images --prepare_dataset --captioning_model blip

# Reduce batch size
python train.py --model flux --input_dir /path/to/images --prepare_dataset --batch_size 4
```

## 📈 Performance Tips

### For A40 GPUs
1. **Use BF16**: Better than FP16 for A40
2. **Large Batches**: Utilize 48GB VRAM efficiently
3. **Variable Size**: Better quality than fixed-size training
4. **XFormers**: Enable memory efficient attention

### Dataset Optimization
1. **Quality Filtering**: Use threshold 0.7+ for better results
2. **Resolution**: 768-1024px optimal for most use cases
3. **Aspect Ratios**: Mixed ratios work well with variable-size training
4. **Captions**: BLIP2 provides best quality

### Training Optimization
1. **Learning Rate**: 1e-4 works well for most cases
2. **Warmup**: 500 steps recommended
3. **Scheduler**: Cosine annealing for stable training
4. **Checkpoints**: Save every 500 steps

## 🎉 Success Examples

### FLUX Training Results
- **Art Style Transfer**: 2-3 hours on A40
- **Product Photography**: 1-2 hours on A40
- **Character Design**: 3-4 hours on A40

### Wan2.1 Training Results
- **Landscape Animation**: 4-6 hours on A40
- **Object Motion**: 2-3 hours on A40
- **Style Transfer**: 5-7 hours on A40

## 📚 Additional Resources

### Documentation
- Model configurations in `config/`
- Example datasets in `examples/`
- Troubleshooting guides in docs

### Community
- GitHub Issues for bug reports
- Discussions for questions
- Examples and tutorials

---

**Ready to train world-class diffusion models? Start with:**
```bash
python train.py --model flux --input_dir /your/images --prepare_dataset
```

🎨 **From raw images to trained model in one command!**
