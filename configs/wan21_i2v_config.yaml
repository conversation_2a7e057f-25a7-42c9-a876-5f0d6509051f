# Wan2.1 Image-to-Video Model Configuration
# Optimized for A40 GPU (48GB VRAM)

model:
  model_name: "wan2_1_i2v"
  pretrained_model_path: "Wan-AI/Wan2.1-I2V-14B-720P-Diffusers"
  cache_dir: "./cache"
  revision: null
  torch_dtype: "bfloat16"
  device_map: null
  
  # LoRA Configuration
  use_lora: true
  lora_type: "lora"  # Options: lora, qlora, dora
  lora_rank: 64
  lora_alpha: 64
  lora_dropout: 0.1
  lora_target_modules:
    - "to_q"
    - "to_k"
    - "to_v"
    - "to_out.0"
    - "proj_in"
    - "proj_out"
    - "ff.net.0.proj"
    - "ff.net.2"
    - "conv_in"
    - "conv_out"
  
  # Quantization (for QLoRA)
  use_4bit: false
  use_8bit: false
  bnb_4bit_compute_dtype: "bfloat16"
  bnb_4bit_quant_type: "nf4"
  bnb_4bit_use_double_quant: true

dataset:
  dataset_type: "image_to_video"
  data_dir: "./data"
  image_column: "image"
  caption_column: "caption"
  video_column: "video"
  
  # Video processing
  resolution: [1280, 720]  # Width x Height
  max_resolution: 2048
  min_resolution: 256
  center_crop: true
  random_flip: 0.0
  
  # Video-specific
  num_frames: 16
  frame_rate: 8
  
  # Text processing
  max_sequence_length: 512
  
  # Auto-captioning
  auto_caption: false
  captioning_model: "blip2"
  quality_threshold: 0.7
  
  # Variable size training
  variable_size: true
  bucket_sizes:
    - [1280, 720]   # 16:9 HD
    - [1024, 576]   # 16:9 smaller
    - [960, 540]    # 16:9 compact
    - [1152, 648]   # 16:9 medium
    - [720, 720]    # Square
    - [720, 1280]   # 9:16 vertical

training:
  output_dir: "./outputs/wan21_i2v"
  logging_dir: "./outputs/wan21_i2v/logs"
  
  # Training parameters
  num_train_epochs: 1
  max_train_steps: 1000
  train_batch_size: 1
  gradient_accumulation_steps: 4
  learning_rate: 5e-5
  lr_scheduler: "cosine"
  lr_warmup_steps: 100
  lr_num_cycles: 1
  
  # Optimization
  use_8bit_adam: false
  adam_beta1: 0.9
  adam_beta2: 0.999
  adam_weight_decay: 0.01
  adam_epsilon: 1e-8
  max_grad_norm: 1.0
  
  # Mixed precision and acceleration
  mixed_precision: "bf16"
  gradient_checkpointing: true
  
  # Logging and validation
  logging_steps: 10
  validation_steps: 500
  checkpointing_steps: 500
  
  # Reproducibility
  seed: 42
  
  # Monitoring
  report_to: null  # Options: wandb, tensorboard
  
  # Hardware optimization
  dataloader_num_workers: 2
  pin_memory: true

# Validation settings
validation:
  validation_prompts:
    - "A person walking through a beautiful garden"
    - "Ocean waves crashing on a beach"
    - "A cat playing with a ball of yarn"
    - "Clouds moving across a blue sky"
    - "A flower blooming in time-lapse"
  guidance_scale: 7.5
  num_inference_steps: 20
  num_frames: 16

# W&B Configuration (optional)
wandb:
  project_name: "wan21-i2v-finetuning"
  run_name: "wan21-i2v-lora-experiment"
  tags:
    - "wan21"
    - "image2video"
    - "lora"
  notes: "Wan2.1 I2V LoRA finetuning experiment"

# Memory optimization settings for A40
memory_optimization:
  # Enable gradient checkpointing
  gradient_checkpointing: true
  
  # Use attention slicing for memory efficiency
  attention_slicing: true
  
  # CPU offloading for large models
  cpu_offload: false
  
  # Memory efficient attention
  use_memory_efficient_attention: true
  
  # Video-specific optimizations
  temporal_attention_optimization: true
  frame_chunking: true
  chunk_size: 4
