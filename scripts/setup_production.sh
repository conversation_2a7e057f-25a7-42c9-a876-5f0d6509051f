#!/bin/bash

# Production Environment Setup Script
# Sets up the complete production environment for diffusion model training

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================================================================${NC}"
}

print_section() {
    echo -e "${BLUE}--- $1 ---${NC}"
}

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    print_warning "Running as root. Consider using a virtual environment instead."
fi

print_header "🏭 Production Environment Setup for Diffusion Model Training"

# Check Python version
print_section "Checking Python Environment"
PYTHON_VERSION=$(python --version 2>&1 | cut -d' ' -f2)
PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)

print_status "Python version: $PYTHON_VERSION"

if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 8 ]); then
    print_error "Python 3.8+ required. Current version: $PYTHON_VERSION"
    exit 1
fi

# Check if in virtual environment
if [ -z "$VIRTUAL_ENV" ] && [ -z "$CONDA_DEFAULT_ENV" ]; then
    print_warning "Not in a virtual environment. Highly recommended to use one."
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Please create and activate a virtual environment:"
        echo "  python -m venv venv"
        echo "  source venv/bin/activate  # Linux/Mac"
        echo "  # or"
        echo "  conda create -n diffusion python=3.10"
        echo "  conda activate diffusion"
        exit 1
    fi
else
    print_status "Virtual environment detected: ${VIRTUAL_ENV:-$CONDA_DEFAULT_ENV}"
fi

# Check CUDA availability
print_section "Checking CUDA Environment"
if command -v nvidia-smi &> /dev/null; then
    print_status "NVIDIA GPU detected:"
    nvidia-smi --query-gpu=name,driver_version,memory.total --format=csv,noheader | while read line; do
        echo "  $line"
    done
    
    # Check CUDA version
    if command -v nvcc &> /dev/null; then
        CUDA_VERSION=$(nvcc --version | grep "release" | sed 's/.*release \([0-9]\+\.[0-9]\+\).*/\1/')
        print_status "CUDA version: $CUDA_VERSION"
    else
        print_warning "nvcc not found. CUDA development tools may not be installed."
    fi
else
    print_warning "No NVIDIA GPU detected. Training will use CPU (very slow)."
fi

# Create directory structure
print_section "Creating Directory Structure"
DIRS=("logs" "outputs" "cache" "data" "data/train" "data/validation" "tensorboard_logs" "dataset_cache")

for dir in "${DIRS[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        print_status "Created directory: $dir"
    else
        print_status "Directory exists: $dir"
    fi
done

# Setup .env file
print_section "Setting up Environment Configuration"
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        cp .env.example .env
        print_status "Created .env from .env.example"
        print_warning "Please edit .env file with your actual configuration"
    else
        print_error ".env.example not found!"
        exit 1
    fi
else
    print_status ".env file already exists"
fi

# Install Python dependencies
print_section "Installing Python Dependencies"

# Check if requirements.txt exists
if [ ! -f "requirements.txt" ]; then
    print_error "requirements.txt not found!"
    exit 1
fi

print_status "Installing base requirements..."
pip install -r requirements.txt

# Install additional production dependencies
print_status "Installing production dependencies..."
pip install python-dotenv psutil

# Optional dependencies
print_status "Installing optional dependencies..."

# Install wandb for experiment tracking
if ! pip show wandb &> /dev/null; then
    print_status "Installing Weights & Biases..."
    pip install wandb
fi

# Install bitsandbytes for QLoRA
if ! pip show bitsandbytes &> /dev/null; then
    print_status "Installing bitsandbytes for QLoRA support..."
    pip install bitsandbytes
fi

# Install xformers for memory efficiency
if ! pip show xformers &> /dev/null; then
    print_status "Installing xformers for memory efficiency..."
    pip install xformers
fi

# Verify installations
print_section "Verifying Installation"

print_status "Checking core dependencies..."
python -c "
import sys
import torch
import diffusers
import transformers
import accelerate
import peft

print(f'✅ Python: {sys.version}')
print(f'✅ PyTorch: {torch.__version__}')
print(f'✅ Diffusers: {diffusers.__version__}')
print(f'✅ Transformers: {transformers.__version__}')
print(f'✅ Accelerate: {accelerate.__version__}')
print(f'✅ PEFT: {peft.__version__}')

if torch.cuda.is_available():
    print(f'✅ CUDA: {torch.version.cuda}')
    print(f'✅ GPU: {torch.cuda.get_device_name(0)}')
    print(f'✅ GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB')
else:
    print('⚠️  CUDA not available')
"

# Check optional dependencies
print_status "Checking optional dependencies..."
python -c "
try:
    import wandb
    print('✅ Weights & Biases available')
except ImportError:
    print('⚠️  Weights & Biases not available')

try:
    import bitsandbytes
    print('✅ BitsAndBytes available (QLoRA support)')
except ImportError:
    print('⚠️  BitsAndBytes not available (no QLoRA support)')

try:
    import xformers
    print('✅ xFormers available (memory optimization)')
except ImportError:
    print('⚠️  xFormers not available (limited memory optimization)')
"

# Test configuration manager
print_section "Testing Configuration System"
python -c "
try:
    from src.config_manager import get_config_manager
    config_mgr = get_config_manager()
    config_mgr.print_config_summary()
    print('✅ Configuration system working')
except Exception as e:
    print(f'❌ Configuration system error: {e}')
    exit(1)
"

# Make scripts executable
print_section "Setting Script Permissions"
SCRIPTS=("scripts/train_production.sh" "scripts/train_lora.sh" "scripts/train_flux.sh" "scripts/train_variable_size.sh" "scripts/inference.sh" "scripts/inference_flux.sh")

for script in "${SCRIPTS[@]}"; do
    if [ -f "$script" ]; then
        chmod +x "$script"
        print_status "Made executable: $script"
    fi
done

# Setup git hooks (if in git repo)
if [ -d ".git" ]; then
    print_section "Setting up Git Hooks"
    
    # Create pre-commit hook to prevent committing .env
    if [ ! -f ".git/hooks/pre-commit" ]; then
        cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# Prevent committing .env files
if git diff --cached --name-only | grep -q "^\.env$"; then
    echo "Error: Attempting to commit .env file"
    echo "This file contains sensitive information and should not be committed"
    exit 1
fi
EOF
        chmod +x .git/hooks/pre-commit
        print_status "Created git pre-commit hook to prevent .env commits"
    fi
fi

# Create .gitignore entries
print_section "Updating .gitignore"
GITIGNORE_ENTRIES=(
    ".env"
    "logs/"
    "outputs/"
    "cache/"
    "dataset_cache/"
    "tensorboard_logs/"
    "*.log"
    "__pycache__/"
    "*.pyc"
    ".DS_Store"
    "wandb/"
)

if [ -f ".gitignore" ]; then
    for entry in "${GITIGNORE_ENTRIES[@]}"; do
        if ! grep -q "^$entry" .gitignore; then
            echo "$entry" >> .gitignore
            print_status "Added to .gitignore: $entry"
        fi
    done
else
    printf '%s\n' "${GITIGNORE_ENTRIES[@]}" > .gitignore
    print_status "Created .gitignore file"
fi

# Final setup verification
print_section "Final Verification"
python -c "
from src.config_manager import get_config_manager
from src.auth_manager import create_auth_manager

print('🔧 Testing production setup...')

try:
    config_mgr = get_config_manager()
    auth_mgr = create_auth_manager(config_mgr)
    print('✅ Production environment setup successful')
except Exception as e:
    print(f'❌ Setup verification failed: {e}')
    exit(1)
"

print_header "🎉 Production Environment Setup Complete!"

echo ""
print_status "Setup Summary:"
echo "  ✅ Python environment verified"
echo "  ✅ Dependencies installed"
echo "  ✅ Directory structure created"
echo "  ✅ Configuration system ready"
echo "  ✅ Scripts made executable"
echo "  ✅ Git hooks configured"
echo ""

print_header "🚀 Next Steps:"
echo ""
echo "1. 📝 Configure your environment:"
echo "   Edit .env file with your API keys and settings"
echo "   At minimum, set HF_TOKEN for model access"
echo ""
echo "2. 🔑 Setup authentication:"
echo "   - Get HF token: https://huggingface.co/settings/tokens"
echo "   - Get W&B key (optional): https://wandb.ai/authorize"
echo ""
echo "3. 📊 Prepare your dataset:"
echo "   ./scripts/prepare_dataset.py --help"
echo "   ./scripts/prepare_flux_dataset.py --help"
echo ""
echo "4. 🚀 Start training:"
echo "   ./scripts/train_production.sh wan2.1 config/wan2_1_config.yaml"
echo "   ./scripts/train_production.sh flux config/flux_lora_config.yaml"
echo ""
echo "5. 📖 Read the documentation:"
echo "   - README.md for general overview"
echo "   - FLUX_TRAINING_GUIDE.md for FLUX-specific guide"
echo ""

print_warning "Remember to edit .env file before training!"
print_status "Production environment is ready for diffusion model training! 🎨"
