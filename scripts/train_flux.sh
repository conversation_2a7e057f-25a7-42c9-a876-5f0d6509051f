#!/bin/bash

# FLUX.1-dev LoRA/QLoRA Training Script
# Usage: ./scripts/train_flux.sh [lora_type] [additional_args...]

set -e

# Parse arguments
LORA_TYPE="${1:-lora}"  # Default to standard LoRA
shift || true  # Remove first argument if it exists

# Validate LoRA type
case "$LORA_TYPE" in
    "lora")
        CONFIG_FILE="config/flux_lora_config.yaml"
        echo "Using FLUX LoRA configuration"
        ;;
    "qlora")
        CONFIG_FILE="config/flux_qlora_config.yaml"
        echo "Using FLUX QLoRA (4-bit quantization) configuration"
        ;;
    *)
        echo "Error: Invalid LoRA type '$LORA_TYPE'"
        echo "Valid options: lora, qlora"
        echo "Usage: $0 [lora_type] [additional_args...]"
        exit 1
        ;;
esac

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo "Error: Config file '$CONFIG_FILE' not found!"
    exit 1
fi

echo "Starting FLUX.1-dev $LORA_TYPE finetuning with config: $CONFIG_FILE"
echo "Additional arguments: $@"

# Set environment variables for optimization
export CUDA_VISIBLE_DEVICES=${CUDA_VISIBLE_DEVICES:-0}
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
export TOKENIZERS_PARALLELISM=false

# Memory optimization for QLoRA
if [ "$LORA_TYPE" = "qlora" ]; then
    export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:256
    echo "Applied QLoRA memory optimizations"
fi

# Check GPU memory and provide recommendations
if command -v nvidia-smi &> /dev/null; then
    echo "GPU Information:"
    nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv,noheader,nounits
    
    # Get available memory in MB
    AVAILABLE_MEMORY=$(nvidia-smi --query-gpu=memory.free --format=csv,noheader,nounits | head -1)
    
    echo ""
    echo "FLUX.1-dev memory recommendations:"
    if [ "$AVAILABLE_MEMORY" -lt 16000 ]; then
        echo "⚠️  Low GPU memory detected (<16GB). Consider:"
        echo "   - Using QLoRA: ./scripts/train_flux.sh qlora"
        echo "   - Reducing resolution to 768x768"
        echo "   - Using smaller batch size"
    elif [ "$AVAILABLE_MEMORY" -lt 24000 ]; then
        echo "✅ Moderate GPU memory (16-24GB). Recommended:"
        echo "   - QLoRA for best memory efficiency: ./scripts/train_flux.sh qlora"
        echo "   - Standard LoRA with optimizations: ./scripts/train_flux.sh lora"
    else
        echo "✅ High GPU memory (>24GB). You can use:"
        echo "   - Any LoRA type (lora/qlora)"
        echo "   - Full resolution training (1024x1024)"
    fi
    echo ""
fi

# Create necessary directories
mkdir -p logs
mkdir -p outputs

# Run training with appropriate settings
echo "Starting training..."

if [ "$LORA_TYPE" = "qlora" ]; then
    # QLoRA specific optimizations
    echo "Applying QLoRA optimizations..."
    python src/train_flux.py \
        --config "$CONFIG_FILE" \
        --mixed_precision "bf16" \
        "$@"
else
    # Standard LoRA training
    python src/train_flux.py \
        --config "$CONFIG_FILE" \
        "$@"
fi

echo "Training completed!"
echo ""
echo "Next steps:"
echo "1. Check training logs in ./logs/"
echo "2. Find model weights in ./outputs/"
echo "3. Test inference with: python src/inference_flux.py --lora_path ./outputs/[model_dir]/lora --prompt 'your prompt'"
