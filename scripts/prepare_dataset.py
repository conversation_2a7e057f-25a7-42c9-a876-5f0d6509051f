#!/usr/bin/env python3
"""
Dataset preparation script for Wan2.1 finetuning
"""

import os
import json
import argparse
import shutil
from pathlib import Path
from typing import List, Dict, Any
import cv2
from PIL import Image
import numpy as np


def extract_first_frame(video_path: str, output_path: str) -> bool:
    """Extract first frame from video as image"""
    try:
        cap = cv2.VideoCapture(video_path)
        ret, frame = cap.read()
        cap.release()
        
        if ret:
            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            image = Image.fromarray(frame_rgb)
            image.save(output_path)
            return True
        return False
    except Exception as e:
        print(f"Error extracting frame from {video_path}: {e}")
        return False


def validate_video(video_path: str, min_duration: float = 1.0) -> Dict[str, Any]:
    """Validate video file and return metadata"""
    try:
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            return {"valid": False, "error": "Cannot open video"}
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        cap.release()
        
        duration = frame_count / fps if fps > 0 else 0
        
        # Validation checks
        if duration < min_duration:
            return {"valid": False, "error": f"Video too short: {duration:.2f}s < {min_duration}s"}
        
        if width < 256 or height < 256:
            return {"valid": False, "error": f"Resolution too low: {width}x{height}"}
        
        return {
            "valid": True,
            "duration": duration,
            "fps": fps,
            "frame_count": frame_count,
            "width": width,
            "height": height,
        }
        
    except Exception as e:
        return {"valid": False, "error": str(e)}


def create_dataset_from_videos(
    input_dir: str,
    output_dir: str,
    extract_images: bool = True,
    min_duration: float = 1.0,
    max_samples: int = None,
):
    """
    Create dataset structure from a directory of videos
    
    Args:
        input_dir: Directory containing video files
        output_dir: Output directory for organized dataset
        extract_images: Whether to extract first frames as images
        min_duration: Minimum video duration in seconds
        max_samples: Maximum number of samples to process
    """
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    # Create output directories
    (output_path / "images").mkdir(parents=True, exist_ok=True)
    (output_path / "videos").mkdir(parents=True, exist_ok=True)
    
    # Find all video files
    video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.webm'}
    video_files = []
    
    for ext in video_extensions:
        video_files.extend(input_path.glob(f"*{ext}"))
        video_files.extend(input_path.glob(f"*{ext.upper()}"))
    
    print(f"Found {len(video_files)} video files")
    
    if max_samples:
        video_files = video_files[:max_samples]
        print(f"Processing first {max_samples} videos")
    
    metadata = []
    valid_count = 0
    
    for i, video_file in enumerate(video_files):
        print(f"Processing {i+1}/{len(video_files)}: {video_file.name}")
        
        # Validate video
        video_info = validate_video(str(video_file), min_duration)
        
        if not video_info["valid"]:
            print(f"  Skipping: {video_info['error']}")
            continue
        
        # Copy video to output directory
        video_output_path = output_path / "videos" / f"video_{valid_count:06d}.mp4"
        shutil.copy2(video_file, video_output_path)
        
        # Extract first frame if requested
        image_output_path = output_path / "images" / f"image_{valid_count:06d}.jpg"
        
        if extract_images:
            if extract_first_frame(str(video_file), str(image_output_path)):
                print(f"  Extracted first frame")
            else:
                print(f"  Failed to extract first frame, skipping")
                continue
        
        # Create metadata entry
        metadata_entry = {
            "image": f"images/{image_output_path.name}",
            "video": f"videos/{video_output_path.name}",
            "caption": f"Video sample {valid_count + 1}",  # Placeholder caption
            "original_path": str(video_file),
            "duration": video_info["duration"],
            "fps": video_info["fps"],
            "resolution": f"{video_info['width']}x{video_info['height']}",
        }
        
        metadata.append(metadata_entry)
        valid_count += 1
        
        print(f"  Added to dataset (duration: {video_info['duration']:.2f}s, "
              f"resolution: {video_info['width']}x{video_info['height']})")
    
    # Save metadata
    metadata_path = output_path / "metadata.json"
    with open(metadata_path, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print(f"\nDataset creation complete!")
    print(f"Valid samples: {valid_count}")
    print(f"Output directory: {output_path}")
    print(f"Metadata saved to: {metadata_path}")
    print(f"\nNext steps:")
    print(f"1. Edit {metadata_path} to add proper captions for your videos")
    print(f"2. Update the config file to point to {output_path}")
    print(f"3. Run training with: python src/train_wan2_1.py --config config/wan2_1_config.yaml")


def create_dataset_from_pairs(
    images_dir: str,
    videos_dir: str,
    output_dir: str,
    min_duration: float = 1.0,
):
    """
    Create dataset from separate image and video directories
    Assumes images and videos have matching names
    """
    images_path = Path(images_dir)
    videos_path = Path(videos_dir)
    output_path = Path(output_dir)
    
    # Create output directories
    (output_path / "images").mkdir(parents=True, exist_ok=True)
    (output_path / "videos").mkdir(parents=True, exist_ok=True)
    
    # Find matching image-video pairs
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp'}
    video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.webm'}
    
    image_files = {}
    for ext in image_extensions:
        for img_file in images_path.glob(f"*{ext}"):
            stem = img_file.stem
            image_files[stem] = img_file
        for img_file in images_path.glob(f"*{ext.upper()}"):
            stem = img_file.stem
            image_files[stem] = img_file
    
    video_files = {}
    for ext in video_extensions:
        for vid_file in videos_path.glob(f"*{ext}"):
            stem = vid_file.stem
            video_files[stem] = vid_file
        for vid_file in videos_path.glob(f"*{ext.upper()}"):
            stem = vid_file.stem
            video_files[stem] = vid_file
    
    # Find matching pairs
    matching_stems = set(image_files.keys()) & set(video_files.keys())
    print(f"Found {len(matching_stems)} matching image-video pairs")
    
    metadata = []
    valid_count = 0
    
    for stem in sorted(matching_stems):
        image_file = image_files[stem]
        video_file = video_files[stem]
        
        print(f"Processing pair {valid_count + 1}: {stem}")
        
        # Validate video
        video_info = validate_video(str(video_file), min_duration)
        
        if not video_info["valid"]:
            print(f"  Skipping: {video_info['error']}")
            continue
        
        # Copy files to output directory
        image_output_path = output_path / "images" / f"image_{valid_count:06d}{image_file.suffix}"
        video_output_path = output_path / "videos" / f"video_{valid_count:06d}.mp4"
        
        shutil.copy2(image_file, image_output_path)
        shutil.copy2(video_file, video_output_path)
        
        # Create metadata entry
        metadata_entry = {
            "image": f"images/{image_output_path.name}",
            "video": f"videos/{video_output_path.name}",
            "caption": f"Video sample {valid_count + 1}",  # Placeholder caption
            "original_image": str(image_file),
            "original_video": str(video_file),
            "duration": video_info["duration"],
            "fps": video_info["fps"],
            "resolution": f"{video_info['width']}x{video_info['height']}",
        }
        
        metadata.append(metadata_entry)
        valid_count += 1
        
        print(f"  Added to dataset")
    
    # Save metadata
    metadata_path = output_path / "metadata.json"
    with open(metadata_path, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print(f"\nDataset creation complete!")
    print(f"Valid pairs: {valid_count}")
    print(f"Output directory: {output_path}")
    print(f"Metadata saved to: {metadata_path}")


def main():
    parser = argparse.ArgumentParser(description="Prepare dataset for Wan2.1 finetuning")
    parser.add_argument(
        "--mode",
        type=str,
        choices=["videos", "pairs"],
        required=True,
        help="Dataset creation mode: 'videos' (extract images from videos) or 'pairs' (use existing image-video pairs)"
    )
    parser.add_argument(
        "--input_dir",
        type=str,
        required=True,
        help="Input directory containing videos (for 'videos' mode) or images (for 'pairs' mode)"
    )
    parser.add_argument(
        "--videos_dir",
        type=str,
        help="Directory containing videos (required for 'pairs' mode)"
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        required=True,
        help="Output directory for organized dataset"
    )
    parser.add_argument(
        "--min_duration",
        type=float,
        default=1.0,
        help="Minimum video duration in seconds"
    )
    parser.add_argument(
        "--max_samples",
        type=int,
        default=None,
        help="Maximum number of samples to process"
    )
    parser.add_argument(
        "--no_extract_images",
        action="store_true",
        help="Don't extract images from videos (for 'videos' mode)"
    )
    
    args = parser.parse_args()
    
    if args.mode == "videos":
        create_dataset_from_videos(
            input_dir=args.input_dir,
            output_dir=args.output_dir,
            extract_images=not args.no_extract_images,
            min_duration=args.min_duration,
            max_samples=args.max_samples,
        )
    elif args.mode == "pairs":
        if not args.videos_dir:
            parser.error("--videos_dir is required for 'pairs' mode")
        
        create_dataset_from_pairs(
            images_dir=args.input_dir,
            videos_dir=args.videos_dir,
            output_dir=args.output_dir,
            min_duration=args.min_duration,
        )


if __name__ == "__main__":
    main()
