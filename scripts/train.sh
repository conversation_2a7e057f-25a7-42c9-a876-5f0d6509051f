#!/bin/bash

# Wan2.1 Image-to-Video Finetuning Training Script
# Usage: ./scripts/train.sh [config_file] [additional_args...]

set -e

# Default configuration
CONFIG_FILE="${1:-config/wan2_1_config.yaml}"
shift || true  # Remove first argument if it exists

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo "Error: Config file '$CONFIG_FILE' not found!"
    echo "Usage: $0 [config_file] [additional_args...]"
    exit 1
fi

echo "Starting Wan2.1 finetuning with config: $CONFIG_FILE"
echo "Additional arguments: $@"

# Set environment variables for optimization
export CUDA_VISIBLE_DEVICES=${CUDA_VISIBLE_DEVICES:-0}
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
export TOKENIZERS_PARALLELISM=false

# Check if we have enough GPU memory
if command -v nvidia-smi &> /dev/null; then
    echo "GPU Information:"
    nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv,noheader,nounits
    echo ""
fi

# Create necessary directories
mkdir -p logs
mkdir -p outputs

# Run training
echo "Starting training..."
python src/train_wan2_1.py \
    --config "$CONFIG_FILE" \
    "$@"

echo "Training completed!"
