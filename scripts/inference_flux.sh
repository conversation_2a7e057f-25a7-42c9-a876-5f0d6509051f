#!/bin/bash

# FLUX.1-dev Inference Script
# Usage: ./scripts/inference_flux.sh --prompt "your prompt"

set -e

# Default values
BASE_MODEL="black-forest-labs/FLUX.1-dev"
LORA_PATH=""
OUTPUT_PATH="generated_image.png"
WIDTH=1024
HEIGHT=1024
GUIDANCE_SCALE=3.5
NUM_INFERENCE_STEPS=28
DEVICE="cuda"
NUM_IMAGES=1

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --base_model_path)
            BASE_MODEL="$2"
            shift 2
            ;;
        --lora_path)
            LORA_PATH="$2"
            shift 2
            ;;
        --prompt)
            PROMPT="$2"
            shift 2
            ;;
        --negative_prompt)
            NEGATIVE_PROMPT="$2"
            shift 2
            ;;
        --output_path)
            OUTPUT_PATH="$2"
            shift 2
            ;;
        --width)
            WIDTH="$2"
            shift 2
            ;;
        --height)
            HEIGHT="$2"
            shift 2
            ;;
        --guidance_scale)
            GUIDANCE_SCALE="$2"
            shift 2
            ;;
        --num_inference_steps)
            NUM_INFERENCE_STEPS="$2"
            shift 2
            ;;
        --seed)
            SEED="$2"
            shift 2
            ;;
        --num_images)
            NUM_IMAGES="$2"
            shift 2
            ;;
        --device)
            DEVICE="$2"
            shift 2
            ;;
        --batch_prompts)
            BATCH_PROMPTS="$2"
            shift 2
            ;;
        --output_dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --help)
            echo "FLUX.1-dev Text-to-Image Inference Script"
            echo ""
            echo "Usage: $0 --prompt \"your prompt\" [options]"
            echo ""
            echo "Required arguments:"
            echo "  --prompt TEXT              Text prompt for image generation"
            echo ""
            echo "Optional arguments:"
            echo "  --base_model_path PATH     Base model path (default: $BASE_MODEL)"
            echo "  --lora_path PATH           Path to LoRA weights directory"
            echo "  --negative_prompt TEXT     Negative prompt"
            echo "  --output_path PATH         Output image path (default: $OUTPUT_PATH)"
            echo "  --width INT                Image width (default: $WIDTH)"
            echo "  --height INT               Image height (default: $HEIGHT)"
            echo "  --guidance_scale FLOAT     Guidance scale (default: $GUIDANCE_SCALE)"
            echo "  --num_inference_steps INT  Inference steps (default: $NUM_INFERENCE_STEPS)"
            echo "  --seed INT                 Random seed"
            echo "  --num_images INT           Number of images to generate (default: $NUM_IMAGES)"
            echo "  --device STRING            Device (default: $DEVICE)"
            echo "  --batch_prompts PATH       Text file with prompts for batch generation"
            echo "  --output_dir PATH          Output directory for batch generation"
            echo "  --help                     Show this help message"
            echo ""
            echo "Examples:"
            echo "  # Basic generation"
            echo "  $0 --prompt \"A beautiful landscape painting\""
            echo ""
            echo "  # With LoRA"
            echo "  $0 --prompt \"A portrait in MYSTYLE style\" --lora_path ./outputs/flux-lora/lora"
            echo ""
            echo "  # Batch generation"
            echo "  $0 --batch_prompts prompts.txt --output_dir ./batch_outputs"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Check required arguments
if [ -z "$PROMPT" ] && [ -z "$BATCH_PROMPTS" ]; then
    echo "Error: Either --prompt or --batch_prompts is required"
    echo "Use --help for usage information"
    exit 1
fi

echo "Starting FLUX.1-dev inference..."
if [ -n "$PROMPT" ]; then
    echo "Prompt: $PROMPT"
fi
if [ -n "$LORA_PATH" ]; then
    echo "LoRA: $LORA_PATH"
fi
echo "Output: $OUTPUT_PATH"

# Set environment variables
export CUDA_VISIBLE_DEVICES=${CUDA_VISIBLE_DEVICES:-0}
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# Build command
CMD="python src/inference_flux.py"
CMD="$CMD --base_model_path \"$BASE_MODEL\""
CMD="$CMD --output_path \"$OUTPUT_PATH\""
CMD="$CMD --width $WIDTH"
CMD="$CMD --height $HEIGHT"
CMD="$CMD --guidance_scale $GUIDANCE_SCALE"
CMD="$CMD --num_inference_steps $NUM_INFERENCE_STEPS"
CMD="$CMD --device $DEVICE"
CMD="$CMD --num_images $NUM_IMAGES"

if [ -n "$LORA_PATH" ]; then
    CMD="$CMD --lora_path \"$LORA_PATH\""
fi

if [ -n "$PROMPT" ]; then
    CMD="$CMD --prompt \"$PROMPT\""
fi

if [ -n "$NEGATIVE_PROMPT" ]; then
    CMD="$CMD --negative_prompt \"$NEGATIVE_PROMPT\""
fi

if [ -n "$SEED" ]; then
    CMD="$CMD --seed $SEED"
fi

if [ -n "$BATCH_PROMPTS" ]; then
    CMD="$CMD --batch_prompts \"$BATCH_PROMPTS\""
fi

if [ -n "$OUTPUT_DIR" ]; then
    CMD="$CMD --output_dir \"$OUTPUT_DIR\""
fi

# Run inference
echo "Running: $CMD"
eval $CMD

if [ -n "$BATCH_PROMPTS" ]; then
    echo "Batch inference completed! Images saved to: $OUTPUT_DIR"
else
    echo "Inference completed! Image saved to: $OUTPUT_PATH"
fi
