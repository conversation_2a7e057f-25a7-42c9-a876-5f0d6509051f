#!/bin/bash

# Automatic FLUX Dataset Preparation Script
# Takes raw images and automatically generates captions and dataset structure
# Usage: ./scripts/prepare_flux_auto.sh [input_dir] [output_dir] [options...]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Parse arguments
INPUT_DIR="${1:-}"
OUTPUT_DIR="${2:-}"
shift 2 2>/dev/null || true

# Validate arguments
if [ -z "$INPUT_DIR" ] || [ -z "$OUTPUT_DIR" ]; then
    print_error "Usage: $0 [input_dir] [output_dir] [additional_args...]"
    echo ""
    echo "Examples:"
    echo "  $0 /path/to/images ./data/train"
    echo "  $0 /path/to/images ./data/train --captioning_model blip2 --max_images 1000"
    echo "  $0 /path/to/images ./data/train --min_resolution 768 --quality_threshold 0.8"
    echo ""
    echo "Options:"
    echo "  --captioning_model MODEL    Captioning model (blip, blip2, clip, instructblip)"
    echo "  --max_images N              Maximum number of images to process"
    echo "  --min_resolution N          Minimum image resolution (default: 512)"
    echo "  --max_resolution N          Maximum image resolution (default: 2048)"
    echo "  --quality_threshold N       Quality threshold 0-1 (default: 0.7)"
    echo "  --batch_size N              Batch size for captioning (default: 4)"
    echo "  --num_workers N             Number of worker threads (default: 4)"
    echo "  --no_enhance                Disable caption enhancement"
    echo "  --no_copy                   Don't copy images, use references"
    echo "  --cache_dir DIR             Cache directory for models"
    exit 1
fi

# Check if input directory exists
if [ ! -d "$INPUT_DIR" ]; then
    print_error "Input directory '$INPUT_DIR' does not exist!"
    exit 1
fi

print_header "🎨 Automatic FLUX Dataset Preparation"
print_status "Input Directory: $INPUT_DIR"
print_status "Output Directory: $OUTPUT_DIR"
print_status "Additional Arguments: $@"

# Check for .env file and load if exists
if [ -f ".env" ]; then
    print_status "Loading environment configuration..."
    set -a
    source .env
    set +a
fi

# Count images in input directory
IMAGE_COUNT=$(find "$INPUT_DIR" -type f \( -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.png" -o -iname "*.bmp" -o -iname "*.tiff" -o -iname "*.webp" \) | wc -l)
print_status "Found $IMAGE_COUNT images in input directory"

if [ "$IMAGE_COUNT" -eq 0 ]; then
    print_error "No images found in input directory!"
    print_error "Supported formats: JPG, JPEG, PNG, BMP, TIFF, WEBP"
    exit 1
fi

# Check Python environment
print_status "Checking Python environment..."
if ! python -c "import torch; print(f'PyTorch: {torch.__version__}')" 2>/dev/null; then
    print_error "PyTorch not found. Please install required dependencies:"
    echo "pip install torch torchvision transformers"
    exit 1
fi

# Check available captioning models
print_status "Checking available captioning models..."
AVAILABLE_MODELS=$(python -c "
from src.auto_caption import get_available_models
models = get_available_models()
if models:
    print(' '.join(models))
else:
    print('none')
" 2>/dev/null || echo "none")

if [ "$AVAILABLE_MODELS" = "none" ]; then
    print_warning "No captioning models available. Installing dependencies..."
    
    # Install core dependencies
    print_status "Installing transformers and torch..."
    pip install transformers>=4.40.0 torch>=2.0.0 torchvision>=0.15.0 Pillow>=10.0.0
    
    # Try to install optional dependencies
    print_status "Installing optional captioning models..."
    pip install clip-by-openai || print_warning "Could not install CLIP"
    
    # Check again
    AVAILABLE_MODELS=$(python -c "
from src.auto_caption import get_available_models
models = get_available_models()
print(' '.join(models) if models else 'none')
" 2>/dev/null || echo "none")
fi

print_status "Available captioning models: $AVAILABLE_MODELS"

# Check GPU availability
if command -v nvidia-smi &> /dev/null; then
    GPU_MEMORY=$(nvidia-smi --query-gpu=memory.free --format=csv,noheader,nounits | head -1)
    print_status "GPU detected with ${GPU_MEMORY}MB free memory"
    
    # Adjust batch size based on GPU memory
    if [ "$GPU_MEMORY" -lt 4000 ]; then
        print_warning "Low GPU memory detected. Consider using --batch_size 1"
    elif [ "$GPU_MEMORY" -lt 8000 ]; then
        print_status "Moderate GPU memory. Default batch size should work well"
    else
        print_status "High GPU memory. You can use larger batch sizes for faster processing"
    fi
else
    print_warning "No GPU detected. Captioning will use CPU (slower)"
fi

# Estimate processing time
if [ "$IMAGE_COUNT" -gt 0 ]; then
    # Rough estimates: 2-5 seconds per image depending on model and hardware
    ESTIMATED_TIME=$((IMAGE_COUNT * 3))
    ESTIMATED_MINUTES=$((ESTIMATED_TIME / 60))
    
    if [ "$ESTIMATED_MINUTES" -gt 60 ]; then
        ESTIMATED_HOURS=$((ESTIMATED_MINUTES / 60))
        print_status "Estimated processing time: ~${ESTIMATED_HOURS}h ${ESTIMATED_MINUTES}m"
    else
        print_status "Estimated processing time: ~${ESTIMATED_MINUTES}m"
    fi
fi

# Create output directory
print_status "Creating output directory..."
mkdir -p "$OUTPUT_DIR"

# Check disk space
REQUIRED_SPACE_MB=$((IMAGE_COUNT * 2))  # Rough estimate: 2MB per image
AVAILABLE_SPACE_MB=$(df "$OUTPUT_DIR" | tail -1 | awk '{print int($4/1024)}')

if [ "$AVAILABLE_SPACE_MB" -lt "$REQUIRED_SPACE_MB" ]; then
    print_warning "Low disk space. Required: ~${REQUIRED_SPACE_MB}MB, Available: ${AVAILABLE_SPACE_MB}MB"
    print_warning "Consider using --no_copy to avoid copying images"
fi

# Build command
CMD="python src/auto_dataset_prep.py"
CMD="$CMD --input_dir \"$INPUT_DIR\""
CMD="$CMD --output_dir \"$OUTPUT_DIR\""

# Add additional arguments
for arg in "$@"; do
    CMD="$CMD \"$arg\""
done

print_header "🚀 Starting Automatic Dataset Preparation"
print_status "Command: $CMD"
echo ""

# Create log file
LOG_FILE="logs/dataset_prep_$(date +%Y%m%d_%H%M%S).log"
mkdir -p logs
print_status "Logs will be saved to: $LOG_FILE"

# Execute preparation
eval $CMD 2>&1 | tee "$LOG_FILE"

# Check exit status
PREP_EXIT_CODE=${PIPESTATUS[0]}

if [ $PREP_EXIT_CODE -eq 0 ]; then
    print_header "✅ Dataset Preparation Completed Successfully!"
    
    # Show dataset info
    if [ -f "$OUTPUT_DIR/metadata.json" ]; then
        DATASET_SIZE=$(python -c "
import json
with open('$OUTPUT_DIR/metadata.json', 'r') as f:
    data = json.load(f)
print(len(data))
" 2>/dev/null || echo "unknown")
        
        print_status "Dataset created with $DATASET_SIZE images"
        print_status "Metadata saved to: $OUTPUT_DIR/metadata.json"
        
        if [ -d "$OUTPUT_DIR/images" ]; then
            print_status "Images saved to: $OUTPUT_DIR/images/"
        fi
    fi
    
    # Show next steps
    echo ""
    print_header "🎯 Next Steps:"
    echo "1. 📝 Review generated captions (optional):"
    echo "   python scripts/review_captions.py --dataset_dir $OUTPUT_DIR"
    echo ""
    echo "2. 🚀 Start FLUX training:"
    echo "   ./scripts/train_production.sh flux config/flux_variable_size_config.yaml --variable_size"
    echo ""
    echo "3. 📊 Monitor training progress:"
    echo "   tail -f logs/training_session_*.log"
    echo ""
    echo "4. 🎨 Generate images after training:"
    echo "   ./scripts/inference_flux.sh --prompt 'your prompt' --lora_path ./outputs/flux-finetuned/lora"
    
else
    print_header "❌ Dataset Preparation Failed!"
    print_error "Preparation exited with code: $PREP_EXIT_CODE"
    print_error "Check the logs for details: $LOG_FILE"
    
    echo ""
    print_header "🔧 Troubleshooting:"
    echo "1. Check input directory contains valid images"
    echo "2. Verify Python dependencies are installed"
    echo "3. Check available disk space"
    echo "4. Review error logs: $LOG_FILE"
    echo "5. Try with smaller batch size: --batch_size 1"
    echo "6. Try different captioning model: --captioning_model blip"
    
    exit $PREP_EXIT_CODE
fi

# Show dataset statistics if available
if [ -f "$OUTPUT_DIR/dataset_stats.json" ]; then
    echo ""
    print_header "📊 Dataset Statistics:"
    python -c "
import json
with open('$OUTPUT_DIR/dataset_stats.json', 'r') as f:
    stats = json.load(f)
    
print(f\"  Total images found: {stats.get('total_found', 0)}\")
print(f\"  Images processed: {stats.get('processed', 0)}\")
print(f\"  Images skipped: {stats.get('skipped', 0)}\")
print(f\"  Captions generated: {stats.get('captions_generated', 0)}\")
print(f\"  Processing time: {stats.get('processing_time', 0):.1f} seconds\")
print(f\"  Captioning model: {stats.get('captioning_model', 'unknown')}\")
" 2>/dev/null || echo "  Statistics not available"
fi

echo ""
print_status "Dataset preparation complete! Ready for FLUX training! 🎨"
