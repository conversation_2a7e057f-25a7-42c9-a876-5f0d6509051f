#!/bin/bash

# Wan2.1 Image-to-Video Inference Script
# Usage: ./scripts/inference.sh --image_path path/to/image.jpg --prompt "your prompt"

set -e

# Default values
BASE_MODEL="Wan-AI/Wan2.1-I2V-14B-720P-Diffusers"
LORA_PATH=""
OUTPUT_PATH="generated_video.mp4"
WIDTH=1280
HEIGHT=720
NUM_FRAMES=81
GUIDANCE_SCALE=5.0
NUM_INFERENCE_STEPS=50
FPS=16
DEVICE="cuda"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --base_model_path)
            BASE_MODEL="$2"
            shift 2
            ;;
        --lora_path)
            LORA_PATH="$2"
            shift 2
            ;;
        --image_path)
            IMAGE_PATH="$2"
            shift 2
            ;;
        --prompt)
            PROMPT="$2"
            shift 2
            ;;
        --negative_prompt)
            NEGATIVE_PROMPT="$2"
            shift 2
            ;;
        --output_path)
            OUTPUT_PATH="$2"
            shift 2
            ;;
        --width)
            WIDTH="$2"
            shift 2
            ;;
        --height)
            HEIGHT="$2"
            shift 2
            ;;
        --num_frames)
            NUM_FRAMES="$2"
            shift 2
            ;;
        --guidance_scale)
            GUIDANCE_SCALE="$2"
            shift 2
            ;;
        --num_inference_steps)
            NUM_INFERENCE_STEPS="$2"
            shift 2
            ;;
        --fps)
            FPS="$2"
            shift 2
            ;;
        --seed)
            SEED="$2"
            shift 2
            ;;
        --device)
            DEVICE="$2"
            shift 2
            ;;
        --help)
            echo "Wan2.1 Image-to-Video Inference Script"
            echo ""
            echo "Usage: $0 --image_path path/to/image.jpg --prompt \"your prompt\" [options]"
            echo ""
            echo "Required arguments:"
            echo "  --image_path PATH          Path to input image"
            echo "  --prompt TEXT              Text prompt for video generation"
            echo ""
            echo "Optional arguments:"
            echo "  --base_model_path PATH     Base model path (default: $BASE_MODEL)"
            echo "  --lora_path PATH           Path to LoRA weights directory"
            echo "  --negative_prompt TEXT     Negative prompt"
            echo "  --output_path PATH         Output video path (default: $OUTPUT_PATH)"
            echo "  --width INT                Video width (default: $WIDTH)"
            echo "  --height INT               Video height (default: $HEIGHT)"
            echo "  --num_frames INT           Number of frames (default: $NUM_FRAMES)"
            echo "  --guidance_scale FLOAT     Guidance scale (default: $GUIDANCE_SCALE)"
            echo "  --num_inference_steps INT  Inference steps (default: $NUM_INFERENCE_STEPS)"
            echo "  --fps INT                  Output FPS (default: $FPS)"
            echo "  --seed INT                 Random seed"
            echo "  --device STRING            Device (default: $DEVICE)"
            echo "  --help                     Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Check required arguments
if [ -z "$IMAGE_PATH" ]; then
    echo "Error: --image_path is required"
    echo "Use --help for usage information"
    exit 1
fi

if [ -z "$PROMPT" ]; then
    echo "Error: --prompt is required"
    echo "Use --help for usage information"
    exit 1
fi

# Check if image file exists
if [ ! -f "$IMAGE_PATH" ]; then
    echo "Error: Image file '$IMAGE_PATH' not found!"
    exit 1
fi

echo "Starting Wan2.1 inference..."
echo "Image: $IMAGE_PATH"
echo "Prompt: $PROMPT"
echo "Output: $OUTPUT_PATH"

# Set environment variables
export CUDA_VISIBLE_DEVICES=${CUDA_VISIBLE_DEVICES:-0}
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# Build command
CMD="python src/inference.py"
CMD="$CMD --base_model_path \"$BASE_MODEL\""
CMD="$CMD --image_path \"$IMAGE_PATH\""
CMD="$CMD --prompt \"$PROMPT\""
CMD="$CMD --output_path \"$OUTPUT_PATH\""
CMD="$CMD --width $WIDTH"
CMD="$CMD --height $HEIGHT"
CMD="$CMD --num_frames $NUM_FRAMES"
CMD="$CMD --guidance_scale $GUIDANCE_SCALE"
CMD="$CMD --num_inference_steps $NUM_INFERENCE_STEPS"
CMD="$CMD --fps $FPS"
CMD="$CMD --device $DEVICE"

if [ -n "$LORA_PATH" ]; then
    CMD="$CMD --lora_path \"$LORA_PATH\""
fi

if [ -n "$NEGATIVE_PROMPT" ]; then
    CMD="$CMD --negative_prompt \"$NEGATIVE_PROMPT\""
fi

if [ -n "$SEED" ]; then
    CMD="$CMD --seed $SEED"
fi

# Run inference
echo "Running: $CMD"
eval $CMD

echo "Inference completed! Video saved to: $OUTPUT_PATH"
