#!/bin/bash

# Production Training Script with Environment Management
# Usage: ./scripts/train_production.sh [model_type] [config_file] [options...]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Parse arguments
MODEL_TYPE="${1:-}"
CONFIG_FILE="${2:-}"
shift 2 2>/dev/null || true

# Validate arguments
if [ -z "$MODEL_TYPE" ] || [ -z "$CONFIG_FILE" ]; then
    print_error "Usage: $0 [model_type] [config_file] [additional_args...]"
    echo ""
    echo "Model types: wan2.1, flux"
    echo "Config files:"
    echo "  - config/wan2_1_config.yaml (standard Wan2.1)"
    echo "  - config/wan2_1_variable_size_config.yaml (variable-size Wan2.1)"
    echo "  - config/flux_lora_config.yaml (standard FLUX)"
    echo "  - config/flux_variable_size_config.yaml (variable-size FLUX)"
    echo ""
    echo "Examples:"
    echo "  $0 wan2.1 config/wan2_1_config.yaml"
    echo "  $0 flux config/flux_lora_config.yaml --variable_size"
    exit 1
fi

# Validate model type
case "$MODEL_TYPE" in
    "wan2.1"|"flux")
        ;;
    *)
        print_error "Invalid model type '$MODEL_TYPE'"
        print_error "Valid options: wan2.1, flux"
        exit 1
        ;;
esac

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    print_error "Config file '$CONFIG_FILE' not found!"
    exit 1
fi

print_header "🏭 Production Training Setup"
print_status "Model Type: $MODEL_TYPE"
print_status "Config File: $CONFIG_FILE"
print_status "Additional Arguments: $@"

# Check for .env file
if [ ! -f ".env" ]; then
    print_warning "No .env file found!"
    print_warning "Copy .env.example to .env and configure your settings"
    
    if [ -f ".env.example" ]; then
        print_status "Creating .env from .env.example..."
        cp .env.example .env
        print_warning "Please edit .env file with your actual configuration before running training"
        print_warning "At minimum, set HF_TOKEN for model access"
        exit 1
    else
        print_error ".env.example not found. Please create .env file manually"
        exit 1
    fi
fi

# Load environment variables
print_status "Loading environment configuration..."
set -a  # Automatically export all variables
source .env
set +a

# Validate critical environment variables
print_status "Validating environment configuration..."

# Check HF_TOKEN
if [ -z "$HF_TOKEN" ] || [ "$HF_TOKEN" = "your_huggingface_token_here" ]; then
    print_warning "HF_TOKEN not set or using placeholder value"
    print_warning "Some models may not be accessible without authentication"
fi

# Check CUDA availability
if command -v nvidia-smi &> /dev/null; then
    print_status "GPU Information:"
    nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv,noheader,nounits | while read line; do
        echo "  $line"
    done
    
    # Get available memory
    AVAILABLE_MEMORY=$(nvidia-smi --query-gpu=memory.free --format=csv,noheader,nounits | head -1)
    
    echo ""
    if [ "$AVAILABLE_MEMORY" -lt 12000 ]; then
        print_warning "Low GPU memory detected (<12GB)"
        print_warning "Consider using QLoRA configuration or reducing batch size"
    elif [ "$AVAILABLE_MEMORY" -lt 24000 ]; then
        print_status "Moderate GPU memory (12-24GB) - Good for most configurations"
    else
        print_status "High GPU memory (>24GB) - Optimal for all configurations"
    fi
else
    print_warning "nvidia-smi not found. GPU status unknown"
fi

# Create necessary directories
print_status "Creating directories..."
mkdir -p logs outputs cache data

# Set environment variables for optimization
export CUDA_VISIBLE_DEVICES=${CUDA_VISIBLE_DEVICES:-0}
export PYTORCH_CUDA_ALLOC_CONF=${PYTORCH_CUDA_ALLOC_CONF:-max_split_size_mb:512}
export TOKENIZERS_PARALLELISM=${TOKENIZERS_PARALLELISM:-false}

# Check if this is variable-size training
VARIABLE_SIZE_FLAG=""
for arg in "$@"; do
    if [ "$arg" = "--variable_size" ]; then
        VARIABLE_SIZE_FLAG="--variable_size"
        print_status "Variable-size training enabled"
        break
    fi
done

# Determine if we need special memory optimizations
if [ "$AVAILABLE_MEMORY" -lt 16000 ] 2>/dev/null; then
    print_status "Applying memory optimizations for limited GPU memory"
    export PYTORCH_CUDA_ALLOC_CONF="max_split_size_mb:256"
fi

# Pre-flight checks
print_header "🔍 Pre-flight Checks"

# Check Python environment
print_status "Checking Python environment..."
if ! python -c "import torch; print(f'PyTorch: {torch.__version__}')" 2>/dev/null; then
    print_error "PyTorch not found or not working"
    exit 1
fi

if ! python -c "import diffusers; print(f'Diffusers: {diffusers.__version__}')" 2>/dev/null; then
    print_error "Diffusers not found or not working"
    exit 1
fi

# Check model access
print_status "Checking model access..."
python -c "
from src.config_manager import get_config_manager
from src.auth_manager import create_auth_manager

config_mgr = get_config_manager()
auth_mgr = create_auth_manager(config_mgr)

models_to_check = []
if '$MODEL_TYPE' == 'wan2.1':
    models_to_check.append('Wan-AI/Wan2.1-I2V-14B-720P-Diffusers')
elif '$MODEL_TYPE' == 'flux':
    models_to_check.append('black-forest-labs/FLUX.1-dev')

for model_id in models_to_check:
    result = auth_mgr.check_model_access(model_id)
    status = '✅' if result['accessible'] else '❌'
    print(f'{status} {model_id}')
    if not result['accessible']:
        print(f'   Error: {result[\"error\"]}')
" || {
    print_error "Model access check failed"
    print_error "Please check your HF_TOKEN and model permissions"
    exit 1
}

# Start training
print_header "🚀 Starting Production Training"

# Build command
CMD="python src/train_production.py"
CMD="$CMD --model_type $MODEL_TYPE"
CMD="$CMD --config $CONFIG_FILE"

# Add variable size flag if present
if [ -n "$VARIABLE_SIZE_FLAG" ]; then
    CMD="$CMD $VARIABLE_SIZE_FLAG"
fi

# Add remaining arguments
for arg in "$@"; do
    if [ "$arg" != "--variable_size" ]; then
        CMD="$CMD \"$arg\""
    fi
done

print_status "Executing: $CMD"
echo ""

# Create a training session log
TRAINING_LOG="logs/training_session_$(date +%Y%m%d_%H%M%S).log"
print_status "Training logs will be saved to: $TRAINING_LOG"

# Execute training with logging
eval $CMD 2>&1 | tee "$TRAINING_LOG"

# Check exit status
TRAINING_EXIT_CODE=${PIPESTATUS[0]}

if [ $TRAINING_EXIT_CODE -eq 0 ]; then
    print_header "✅ Training Completed Successfully!"
    print_status "Model saved to: ${OUTPUT_DIR:-./outputs}"
    print_status "Logs saved to: $TRAINING_LOG"
    
    # Show next steps
    echo ""
    print_header "🎯 Next Steps:"
    if [ "$MODEL_TYPE" = "wan2.1" ]; then
        echo "1. Test video generation:"
        echo "   ./scripts/inference.sh --image_path input.jpg --prompt 'your prompt' --lora_path ./outputs/[model_dir]/lora"
    elif [ "$MODEL_TYPE" = "flux" ]; then
        echo "1. Test image generation:"
        echo "   ./scripts/inference_flux.sh --prompt 'your prompt' --lora_path ./outputs/[model_dir]/lora"
    fi
    echo "2. Check training logs: $TRAINING_LOG"
    echo "3. Monitor system resources: htop or nvidia-smi"
    
else
    print_header "❌ Training Failed!"
    print_error "Training exited with code: $TRAINING_EXIT_CODE"
    print_error "Check the logs for details: $TRAINING_LOG"
    
    # Show common troubleshooting steps
    echo ""
    print_header "🔧 Troubleshooting:"
    echo "1. Check GPU memory: nvidia-smi"
    echo "2. Verify .env configuration"
    echo "3. Check model access permissions"
    echo "4. Review error logs: $TRAINING_LOG"
    echo "5. Try with smaller batch size or QLoRA configuration"
    
    exit $TRAINING_EXIT_CODE
fi
