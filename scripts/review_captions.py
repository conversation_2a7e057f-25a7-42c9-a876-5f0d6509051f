#!/usr/bin/env python3
"""
Interactive caption review and editing tool
Allows manual review and improvement of automatically generated captions
"""

import os
import sys
import json
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from PIL import Image, ImageTk
import threading


class CaptionReviewApp:
    """GUI application for reviewing and editing captions"""
    
    def __init__(self, dataset_dir: str):
        self.dataset_dir = Path(dataset_dir)
        self.metadata_path = self.dataset_dir / "metadata.json"
        
        # Load metadata
        self.load_metadata()
        
        # Current image index
        self.current_index = 0
        self.modified = False
        
        # Setup GUI
        self.setup_gui()
        
        # Load first image
        self.load_current_image()
        
    def load_metadata(self):
        """Load metadata from JSON file"""
        if not self.metadata_path.exists():
            raise FileNotFoundError(f"Metadata file not found: {self.metadata_path}")
            
        with open(self.metadata_path, 'r', encoding='utf-8') as f:
            self.metadata = json.load(f)
            
        print(f"Loaded {len(self.metadata)} images from dataset")
        
    def save_metadata(self):
        """Save metadata back to JSON file"""
        # Create backup
        backup_path = self.metadata_path.with_suffix('.json.backup')
        if self.metadata_path.exists():
            import shutil
            shutil.copy2(self.metadata_path, backup_path)
            
        # Save updated metadata
        with open(self.metadata_path, 'w', encoding='utf-8') as f:
            json.dump(self.metadata, f, indent=2, ensure_ascii=False)
            
        self.modified = False
        print(f"Metadata saved to {self.metadata_path}")
        print(f"Backup created at {backup_path}")
        
    def setup_gui(self):
        """Setup the GUI interface"""
        self.root = tk.Tk()
        self.root.title(f"Caption Review - {self.dataset_dir.name}")
        self.root.geometry("1200x800")
        
        # Configure grid weights
        self.root.grid_rowconfigure(1, weight=1)
        self.root.grid_columnconfigure(1, weight=1)
        
        # Top frame - navigation and info
        top_frame = ttk.Frame(self.root)
        top_frame.grid(row=0, column=0, columnspan=2, sticky="ew", padx=5, pady=5)
        
        # Navigation buttons
        ttk.Button(top_frame, text="◀ Previous", command=self.previous_image).pack(side=tk.LEFT, padx=5)
        ttk.Button(top_frame, text="Next ▶", command=self.next_image).pack(side=tk.LEFT, padx=5)
        
        # Image counter
        self.counter_label = ttk.Label(top_frame, text="")
        self.counter_label.pack(side=tk.LEFT, padx=20)
        
        # Save button
        ttk.Button(top_frame, text="💾 Save", command=self.save_metadata).pack(side=tk.RIGHT, padx=5)
        
        # Jump to image
        ttk.Label(top_frame, text="Go to:").pack(side=tk.RIGHT, padx=5)
        self.jump_entry = ttk.Entry(top_frame, width=10)
        self.jump_entry.pack(side=tk.RIGHT, padx=5)
        self.jump_entry.bind("<Return>", self.jump_to_image)
        
        # Left frame - image display
        left_frame = ttk.Frame(self.root)
        left_frame.grid(row=1, column=0, sticky="nsew", padx=5, pady=5)
        left_frame.grid_rowconfigure(0, weight=1)
        left_frame.grid_columnconfigure(0, weight=1)
        
        # Image canvas with scrollbars
        canvas_frame = ttk.Frame(left_frame)
        canvas_frame.grid(row=0, column=0, sticky="nsew")
        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)
        
        self.image_canvas = tk.Canvas(canvas_frame, bg="white")
        self.image_canvas.grid(row=0, column=0, sticky="nsew")
        
        # Scrollbars for image
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.image_canvas.yview)
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        self.image_canvas.configure(yscrollcommand=v_scrollbar.set)
        
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient="horizontal", command=self.image_canvas.xview)
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        self.image_canvas.configure(xscrollcommand=h_scrollbar.set)
        
        # Right frame - caption editing
        right_frame = ttk.Frame(self.root)
        right_frame.grid(row=1, column=1, sticky="nsew", padx=5, pady=5)
        right_frame.grid_rowconfigure(2, weight=1)
        right_frame.grid_columnconfigure(0, weight=1)
        
        # Image info
        info_frame = ttk.LabelFrame(right_frame, text="Image Information")
        info_frame.grid(row=0, column=0, sticky="ew", pady=5)
        info_frame.grid_columnconfigure(1, weight=1)
        
        self.info_labels = {}
        info_fields = ["Filename", "Dimensions", "Aspect Ratio", "Quality Score", "File Size"]
        
        for i, field in enumerate(info_fields):
            ttk.Label(info_frame, text=f"{field}:").grid(row=i, column=0, sticky="w", padx=5, pady=2)
            label = ttk.Label(info_frame, text="")
            label.grid(row=i, column=1, sticky="w", padx=5, pady=2)
            self.info_labels[field] = label
            
        # Caption editing
        caption_frame = ttk.LabelFrame(right_frame, text="Caption Editing")
        caption_frame.grid(row=1, column=0, sticky="ew", pady=5)
        caption_frame.grid_columnconfigure(0, weight=1)
        
        # Original caption (read-only)
        ttk.Label(caption_frame, text="Original Caption:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.original_caption = tk.Text(caption_frame, height=3, wrap=tk.WORD, state=tk.DISABLED)
        self.original_caption.grid(row=1, column=0, sticky="ew", padx=5, pady=2)
        
        # Current caption (editable)
        ttk.Label(caption_frame, text="Current Caption:").grid(row=2, column=0, sticky="w", padx=5, pady=(10,2))
        self.current_caption = tk.Text(caption_frame, height=4, wrap=tk.WORD)
        self.current_caption.grid(row=3, column=0, sticky="ew", padx=5, pady=2)
        self.current_caption.bind("<KeyRelease>", self.on_caption_change)
        
        # Caption suggestions
        suggestions_frame = ttk.LabelFrame(right_frame, text="Caption Suggestions")
        suggestions_frame.grid(row=2, column=0, sticky="nsew", pady=5)
        suggestions_frame.grid_rowconfigure(0, weight=1)
        suggestions_frame.grid_columnconfigure(0, weight=1)
        
        # Suggestions listbox
        self.suggestions_listbox = tk.Listbox(suggestions_frame)
        self.suggestions_listbox.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
        self.suggestions_listbox.bind("<Double-Button-1>", self.apply_suggestion)
        
        # Add some default suggestions
        default_suggestions = [
            "a high-quality photograph of",
            "a detailed image showing",
            "a professional photo of",
            "an artistic representation of",
            "a clear view of",
            "a beautiful scene with",
            "a realistic depiction of",
            "a stunning image of"
        ]
        
        for suggestion in default_suggestions:
            self.suggestions_listbox.insert(tk.END, suggestion)
            
        # Action buttons
        action_frame = ttk.Frame(right_frame)
        action_frame.grid(row=3, column=0, sticky="ew", pady=5)
        
        ttk.Button(action_frame, text="🔄 Reset", command=self.reset_caption).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="✨ Enhance", command=self.enhance_caption).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="🎯 Apply", command=self.apply_caption).pack(side=tk.RIGHT, padx=5)
        
        # Keyboard shortcuts
        self.root.bind("<Left>", lambda e: self.previous_image())
        self.root.bind("<Right>", lambda e: self.next_image())
        self.root.bind("<Control-s>", lambda e: self.save_metadata())
        
        # Handle window closing
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def load_current_image(self):
        """Load and display the current image"""
        if not self.metadata:
            return
            
        current_item = self.metadata[self.current_index]
        
        # Update counter
        self.counter_label.config(text=f"{self.current_index + 1} / {len(self.metadata)}")
        
        # Update image info
        self.update_image_info(current_item)
        
        # Load image
        try:
            image_path = self.dataset_dir / current_item["image"]
            if not image_path.exists():
                # Try original path
                image_path = Path(current_item.get("original_path", ""))
                
            if image_path.exists():
                self.display_image(image_path)
            else:
                self.image_canvas.delete("all")
                self.image_canvas.create_text(200, 200, text="Image not found", font=("Arial", 16))
                
        except Exception as e:
            print(f"Error loading image: {e}")
            self.image_canvas.delete("all")
            self.image_canvas.create_text(200, 200, text=f"Error: {e}", font=("Arial", 12))
            
        # Update captions
        self.update_captions(current_item)
        
    def display_image(self, image_path: Path):
        """Display image on canvas"""
        try:
            # Load and resize image for display
            with Image.open(image_path) as img:
                # Calculate display size (max 600x600)
                max_size = 600
                img.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
                
                # Convert to PhotoImage
                self.photo = ImageTk.PhotoImage(img)
                
                # Clear canvas and display image
                self.image_canvas.delete("all")
                self.image_canvas.create_image(0, 0, anchor="nw", image=self.photo)
                
                # Update scroll region
                self.image_canvas.configure(scrollregion=self.image_canvas.bbox("all"))
                
        except Exception as e:
            print(f"Error displaying image: {e}")
            self.image_canvas.delete("all")
            self.image_canvas.create_text(200, 200, text=f"Display error: {e}", font=("Arial", 12))
            
    def update_image_info(self, item: Dict[str, Any]):
        """Update image information display"""
        filename = Path(item["image"]).name
        dimensions = f"{item.get('width', '?')} x {item.get('height', '?')}"
        aspect_ratio = f"{item.get('aspect_ratio', 0):.2f}"
        quality_score = f"{item.get('quality_score', 0):.2f}"
        file_size = f"{item.get('file_size', 0) / 1024:.1f} KB"
        
        self.info_labels["Filename"].config(text=filename)
        self.info_labels["Dimensions"].config(text=dimensions)
        self.info_labels["Aspect Ratio"].config(text=aspect_ratio)
        self.info_labels["Quality Score"].config(text=quality_score)
        self.info_labels["File Size"].config(text=file_size)
        
    def update_captions(self, item: Dict[str, Any]):
        """Update caption displays"""
        original_caption = item.get("original_caption", item.get("caption", ""))
        current_caption = item.get("caption", "")
        
        # Update original caption
        self.original_caption.config(state=tk.NORMAL)
        self.original_caption.delete(1.0, tk.END)
        self.original_caption.insert(1.0, original_caption)
        self.original_caption.config(state=tk.DISABLED)
        
        # Update current caption
        self.current_caption.delete(1.0, tk.END)
        self.current_caption.insert(1.0, current_caption)
        
        # Store original if not already stored
        if "original_caption" not in item:
            item["original_caption"] = original_caption
            
    def on_caption_change(self, event=None):
        """Handle caption text changes"""
        self.modified = True
        
    def apply_caption(self):
        """Apply current caption to metadata"""
        new_caption = self.current_caption.get(1.0, tk.END).strip()
        self.metadata[self.current_index]["caption"] = new_caption
        self.modified = True
        
    def reset_caption(self):
        """Reset caption to original"""
        current_item = self.metadata[self.current_index]
        original = current_item.get("original_caption", "")
        
        self.current_caption.delete(1.0, tk.END)
        self.current_caption.insert(1.0, original)
        
    def enhance_caption(self):
        """Enhance current caption with suggestions"""
        current_text = self.current_caption.get(1.0, tk.END).strip()
        
        # Simple enhancement: add descriptive words
        enhancements = [
            "high-quality", "detailed", "professional", "artistic",
            "beautiful", "stunning", "realistic", "clear"
        ]
        
        # Check if any enhancement is already present
        current_lower = current_text.lower()
        missing_enhancements = [e for e in enhancements if e not in current_lower]
        
        if missing_enhancements and current_text:
            enhanced = f"{missing_enhancements[0]} {current_text}"
            self.current_caption.delete(1.0, tk.END)
            self.current_caption.insert(1.0, enhanced)
            
    def apply_suggestion(self, event=None):
        """Apply selected suggestion to caption"""
        selection = self.suggestions_listbox.curselection()
        if selection:
            suggestion = self.suggestions_listbox.get(selection[0])
            current_text = self.current_caption.get(1.0, tk.END).strip()
            
            if current_text:
                new_text = f"{suggestion} {current_text}"
            else:
                new_text = suggestion
                
            self.current_caption.delete(1.0, tk.END)
            self.current_caption.insert(1.0, new_text)
            
    def previous_image(self):
        """Go to previous image"""
        if self.current_index > 0:
            self.apply_caption()  # Save current changes
            self.current_index -= 1
            self.load_current_image()
            
    def next_image(self):
        """Go to next image"""
        if self.current_index < len(self.metadata) - 1:
            self.apply_caption()  # Save current changes
            self.current_index += 1
            self.load_current_image()
            
    def jump_to_image(self, event=None):
        """Jump to specific image number"""
        try:
            target = int(self.jump_entry.get()) - 1  # Convert to 0-based index
            if 0 <= target < len(self.metadata):
                self.apply_caption()  # Save current changes
                self.current_index = target
                self.load_current_image()
                self.jump_entry.delete(0, tk.END)
            else:
                messagebox.showerror("Error", f"Image number must be between 1 and {len(self.metadata)}")
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid number")
            
    def on_closing(self):
        """Handle application closing"""
        if self.modified:
            result = messagebox.askyesnocancel(
                "Save Changes",
                "You have unsaved changes. Do you want to save before closing?"
            )
            if result is True:
                self.save_metadata()
                self.root.destroy()
            elif result is False:
                self.root.destroy()
            # If None (cancel), don't close
        else:
            self.root.destroy()
            
    def run(self):
        """Start the GUI application"""
        self.root.mainloop()


def main():
    """Main function for command line usage"""
    parser = argparse.ArgumentParser(description="Review and edit automatically generated captions")
    parser.add_argument("--dataset_dir", type=str, required=True, help="Dataset directory with metadata.json")
    
    args = parser.parse_args()
    
    # Check if dataset directory exists
    dataset_dir = Path(args.dataset_dir)
    if not dataset_dir.exists():
        print(f"❌ Dataset directory not found: {dataset_dir}")
        return
        
    metadata_path = dataset_dir / "metadata.json"
    if not metadata_path.exists():
        print(f"❌ Metadata file not found: {metadata_path}")
        print("Please run dataset preparation first:")
        print(f"./scripts/prepare_flux_auto.sh /path/to/images {dataset_dir}")
        return
        
    print(f"🎨 Starting caption review for dataset: {dataset_dir}")
    print("📝 Use arrow keys to navigate, Ctrl+S to save")
    print("💡 Double-click suggestions to apply them")
    
    try:
        app = CaptionReviewApp(str(dataset_dir))
        app.run()
    except Exception as e:
        print(f"❌ Error starting caption review: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
