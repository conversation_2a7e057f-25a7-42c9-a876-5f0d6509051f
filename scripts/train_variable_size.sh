#!/bin/bash

# Variable Size Training Script for Both Wan2.1 and FLUX
# Usage: ./scripts/train_variable_size.sh [model_type] [additional_args...]

set -e

# Parse arguments
MODEL_TYPE="${1:-wan2.1}"  # Default to wan2.1
shift || true  # Remove first argument if it exists

# Validate model type
case "$MODEL_TYPE" in
    "wan2.1")
        CONFIG_FILE="config/wan2_1_variable_size_config.yaml"
        echo "Using Wan2.1 variable-size configuration"
        ;;
    "flux")
        CONFIG_FILE="config/flux_variable_size_config.yaml"
        echo "Using FLUX variable-size configuration"
        ;;
    *)
        echo "Error: Invalid model type '$MODEL_TYPE'"
        echo "Valid options: wan2.1, flux"
        echo "Usage: $0 [model_type] [additional_args...]"
        exit 1
        ;;
esac

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo "Error: Config file '$CONFIG_FILE' not found!"
    exit 1
fi

echo "Starting $MODEL_TYPE variable-size training with config: $CONFIG_FILE"
echo "Additional arguments: $@"

# Set environment variables for optimization
export CUDA_VISIBLE_DEVICES=${CUDA_VISIBLE_DEVICES:-0}
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
export TOKENIZERS_PARALLELISM=false

# Check GPU memory and provide recommendations
if command -v nvidia-smi &> /dev/null; then
    echo "GPU Information:"
    nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv,noheader,nounits
    
    # Get available memory in MB
    AVAILABLE_MEMORY=$(nvidia-smi --query-gpu=memory.free --format=csv,noheader,nounits | head -1)
    
    echo ""
    echo "Variable-size training memory recommendations:"
    if [ "$AVAILABLE_MEMORY" -lt 16000 ]; then
        echo "⚠️  Low GPU memory detected (<16GB). Consider:"
        echo "   - Using smaller bucket sizes"
        echo "   - Reducing batch size to 1"
        echo "   - Increasing gradient accumulation"
        echo "   - Using QLoRA configuration instead"
    elif [ "$AVAILABLE_MEMORY" -lt 24000 ]; then
        echo "✅ Moderate GPU memory (16-24GB). Recommended:"
        echo "   - Variable-size training should work well"
        echo "   - Monitor memory usage during training"
        echo "   - Adjust bucket sizes if needed"
    else
        echo "✅ High GPU memory (>24GB). You can use:"
        echo "   - Full variable-size training"
        echo "   - Larger bucket sizes for better quality"
        echo "   - Higher batch sizes if desired"
    fi
    echo ""
fi

# Create necessary directories
mkdir -p logs
mkdir -p outputs

# Run variable-size training
echo "Starting variable-size training..."

python src/train_variable_size.py \
    --model_type "$MODEL_TYPE" \
    --config "$CONFIG_FILE" \
    --mixed_precision "bf16" \
    "$@"

echo "Variable-size training completed!"
echo ""
echo "Benefits of variable-size training:"
echo "✅ No image cropping - preserves full content"
echo "✅ Maintains aspect ratios - no distortion"
echo "✅ Efficient batching - groups similar sizes"
echo "✅ Better quality - uses full image information"
echo ""
echo "Next steps:"
echo "1. Check training logs in ./logs/"
echo "2. Find model weights in ./outputs/"
echo "3. Test inference with your trained model"

if [ "$MODEL_TYPE" = "wan2.1" ]; then
    echo "4. Generate videos: ./scripts/inference.sh --lora_path ./outputs/[model_dir]/lora"
else
    echo "4. Generate images: ./scripts/inference_flux.sh --lora_path ./outputs/[model_dir]/lora"
fi
