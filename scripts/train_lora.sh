#!/bin/bash

# Wan2.1 LoRA/QLoRA/DoRA Training Script
# Usage: ./scripts/train_lora.sh [lora_type] [additional_args...]

set -e

# Parse arguments
LORA_TYPE="${1:-lora}"  # Default to standard LoRA
shift || true  # Remove first argument if it exists

# Validate LoRA type
case "$LORA_TYPE" in
    "lora")
        CONFIG_FILE="config/wan2_1_config.yaml"
        echo "Using standard LoRA configuration"
        ;;
    "qlora")
        CONFIG_FILE="config/qlora_config.yaml"
        echo "Using QLoRA (4-bit quantization) configuration"
        ;;
    "dora")
        CONFIG_FILE="config/dora_config.yaml"
        echo "Using DoRA (Weight-Decomposed Low-Rank Adaptation) configuration"
        ;;
    *)
        echo "Error: Invalid LoRA type '$LORA_TYPE'"
        echo "Valid options: lora, qlora, dora"
        echo "Usage: $0 [lora_type] [additional_args...]"
        exit 1
        ;;
esac

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo "Error: Config file '$CONFIG_FILE' not found!"
    exit 1
fi

echo "Starting Wan2.1 $LORA_TYPE finetuning with config: $CONFIG_FILE"
echo "Additional arguments: $@"

# Set environment variables for optimization
export CUDA_VISIBLE_DEVICES=${CUDA_VISIBLE_DEVICES:-0}
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
export TOKENIZERS_PARALLELISM=false

# Memory optimization for QLoRA
if [ "$LORA_TYPE" = "qlora" ]; then
    export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:256
    echo "Applied QLoRA memory optimizations"
fi

# Check GPU memory and provide recommendations
if command -v nvidia-smi &> /dev/null; then
    echo "GPU Information:"
    nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv,noheader,nounits
    
    # Get available memory in MB
    AVAILABLE_MEMORY=$(nvidia-smi --query-gpu=memory.free --format=csv,noheader,nounits | head -1)
    
    echo ""
    echo "Memory recommendations:"
    if [ "$AVAILABLE_MEMORY" -lt 12000 ]; then
        echo "⚠️  Low GPU memory detected (<12GB). Consider:"
        echo "   - Using QLoRA: ./scripts/train_lora.sh qlora"
        echo "   - Reducing batch size and increasing gradient accumulation"
        echo "   - Using smaller resolution/fewer frames"
    elif [ "$AVAILABLE_MEMORY" -lt 24000 ]; then
        echo "✅ Moderate GPU memory (12-24GB). Recommended:"
        echo "   - QLoRA for best memory efficiency: ./scripts/train_lora.sh qlora"
        echo "   - Standard LoRA with optimizations: ./scripts/train_lora.sh lora"
    else
        echo "✅ High GPU memory (>24GB). You can use:"
        echo "   - Any LoRA type (lora/qlora/dora)"
        echo "   - Consider DoRA for potentially better results: ./scripts/train_lora.sh dora"
    fi
    echo ""
fi

# Create necessary directories
mkdir -p logs
mkdir -p outputs

# Run training with appropriate settings
echo "Starting training..."

if [ "$LORA_TYPE" = "qlora" ]; then
    # QLoRA specific optimizations
    echo "Applying QLoRA optimizations..."
    python src/train_wan2_1.py \
        --config "$CONFIG_FILE" \
        --mixed_precision "bf16" \
        "$@"
else
    # Standard LoRA/DoRA training
    python src/train_wan2_1.py \
        --config "$CONFIG_FILE" \
        "$@"
fi

echo "Training completed!"
echo ""
echo "Next steps:"
echo "1. Check training logs in ./logs/"
echo "2. Find model weights in ./outputs/"
echo "3. Test inference with: ./scripts/inference.sh --lora_path ./outputs/[model_dir]/lora"
