#!/usr/bin/env python3
"""
Dataset preparation script for FLUX.1-dev text-to-image finetuning
"""

import os
import json
import argparse
import shutil
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image
import numpy as np


def validate_image(image_path: str, min_size: int = 512) -> Dict[str, Any]:
    """Validate image file and return metadata"""
    try:
        with Image.open(image_path) as img:
            width, height = img.size
            
            # Check minimum size
            if min(width, height) < min_size:
                return {"valid": False, "error": f"Image too small: {width}x{height} < {min_size}"}
            
            # Check format
            if img.format not in ['JPEG', 'PNG', 'WEBP']:
                return {"valid": False, "error": f"Unsupported format: {img.format}"}
            
            return {
                "valid": True,
                "width": width,
                "height": height,
                "format": img.format,
                "mode": img.mode,
            }
            
    except Exception as e:
        return {"valid": False, "error": str(e)}


def resize_image(image_path: str, output_path: str, target_size: int = 1024):
    """Resize image to target size while maintaining aspect ratio"""
    with Image.open(image_path) as img:
        # Convert to RGB if necessary
        if img.mode != 'RGB':
            img = img.convert('RGB')
        
        # Calculate new size maintaining aspect ratio
        width, height = img.size
        if width > height:
            new_width = target_size
            new_height = int(height * target_size / width)
        else:
            new_height = target_size
            new_width = int(width * target_size / height)
        
        # Resize and save
        img_resized = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
        img_resized.save(output_path, quality=95)


def create_text_to_image_dataset(
    input_dir: str,
    output_dir: str,
    resize_images: bool = True,
    target_size: int = 1024,
    min_size: int = 512,
    max_samples: int = None,
):
    """
    Create FLUX text-to-image dataset from directory of images
    
    Args:
        input_dir: Directory containing images
        output_dir: Output directory for organized dataset
        resize_images: Whether to resize images to target size
        target_size: Target size for resizing
        min_size: Minimum image size to accept
        max_samples: Maximum number of samples to process
    """
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    # Create output directories
    (output_path / "images").mkdir(parents=True, exist_ok=True)
    
    # Find all image files
    image_extensions = {'.jpg', '.jpeg', '.png', '.webp', '.bmp'}
    image_files = []
    
    for ext in image_extensions:
        image_files.extend(input_path.glob(f"*{ext}"))
        image_files.extend(input_path.glob(f"*{ext.upper()}"))
    
    print(f"Found {len(image_files)} image files")
    
    if max_samples:
        image_files = image_files[:max_samples]
        print(f"Processing first {max_samples} images")
    
    metadata = []
    valid_count = 0
    
    for i, image_file in enumerate(image_files):
        print(f"Processing {i+1}/{len(image_files)}: {image_file.name}")
        
        # Validate image
        image_info = validate_image(str(image_file), min_size)
        
        if not image_info["valid"]:
            print(f"  Skipping: {image_info['error']}")
            continue
        
        # Determine output filename
        output_filename = f"image_{valid_count:06d}.jpg"
        output_image_path = output_path / "images" / output_filename
        
        # Resize and copy image
        if resize_images:
            resize_image(str(image_file), str(output_image_path), target_size)
            print(f"  Resized and saved to {output_filename}")
        else:
            shutil.copy2(image_file, output_image_path)
            print(f"  Copied to {output_filename}")
        
        # Create caption from filename
        caption = image_file.stem.replace('_', ' ').replace('-', ' ')
        caption = ' '.join(word.capitalize() for word in caption.split())
        
        # Create metadata entry
        metadata_entry = {
            "image": f"images/{output_filename}",
            "caption": f"A photo of {caption}",  # Basic caption template
            "original_path": str(image_file),
            "width": image_info["width"],
            "height": image_info["height"],
            "format": image_info["format"],
        }
        
        metadata.append(metadata_entry)
        valid_count += 1
        
        print(f"  Added to dataset with caption: '{metadata_entry['caption']}'")
    
    # Save metadata
    metadata_path = output_path / "metadata.json"
    with open(metadata_path, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print(f"\nDataset creation complete!")
    print(f"Valid images: {valid_count}")
    print(f"Output directory: {output_path}")
    print(f"Metadata saved to: {metadata_path}")
    print(f"\nNext steps:")
    print(f"1. Edit {metadata_path} to improve captions")
    print(f"2. Update the config file to point to {output_path}")
    print(f"3. Run training with: python src/train_flux.py --config config/flux_lora_config.yaml")


def create_dreambooth_dataset(
    input_dir: str,
    output_dir: str,
    instance_prompt: str = "a photo of sks person",
    class_prompt: str = "a photo of person",
    resize_images: bool = True,
    target_size: int = 1024,
):
    """
    Create FLUX Dreambooth dataset from directory of images
    
    Args:
        input_dir: Directory containing instance images
        output_dir: Output directory for organized dataset
        instance_prompt: Prompt template for instance images
        class_prompt: Prompt template for class images
        resize_images: Whether to resize images
        target_size: Target size for resizing
    """
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    # Create output directories
    (output_path / "instance_images").mkdir(parents=True, exist_ok=True)
    
    # Find all image files
    image_extensions = {'.jpg', '.jpeg', '.png', '.webp', '.bmp'}
    image_files = []
    
    for ext in image_extensions:
        image_files.extend(input_path.glob(f"*{ext}"))
        image_files.extend(input_path.glob(f"*{ext.upper()}"))
    
    print(f"Found {len(image_files)} instance images")
    
    valid_count = 0
    
    for i, image_file in enumerate(image_files):
        print(f"Processing {i+1}/{len(image_files)}: {image_file.name}")
        
        # Validate image
        image_info = validate_image(str(image_file))
        
        if not image_info["valid"]:
            print(f"  Skipping: {image_info['error']}")
            continue
        
        # Determine output filename
        output_filename = f"instance_{valid_count:06d}.jpg"
        output_image_path = output_path / "instance_images" / output_filename
        
        # Resize and copy image
        if resize_images:
            resize_image(str(image_file), str(output_image_path), target_size)
            print(f"  Resized and saved to {output_filename}")
        else:
            shutil.copy2(image_file, output_image_path)
            print(f"  Copied to {output_filename}")
        
        valid_count += 1
    
    # Create config info
    config_info = {
        "instance_data_dir": str(output_path / "instance_images"),
        "instance_prompt": instance_prompt,
        "class_prompt": class_prompt,
        "num_instance_images": valid_count,
        "recommended_steps": max(100, valid_count * 100),  # Rule of thumb
    }
    
    # Save config info
    config_path = output_path / "dreambooth_config.json"
    with open(config_path, 'w') as f:
        json.dump(config_info, f, indent=2)
    
    print(f"\nDreambooth dataset creation complete!")
    print(f"Instance images: {valid_count}")
    print(f"Output directory: {output_path}")
    print(f"Config saved to: {config_path}")
    print(f"\nRecommended training settings:")
    print(f"  Instance prompt: '{instance_prompt}'")
    print(f"  Class prompt: '{class_prompt}'")
    print(f"  Training steps: {config_info['recommended_steps']}")
    print(f"\nNext steps:")
    print(f"1. Update flux config with instance_prompt and class_prompt")
    print(f"2. Set train_data_dir to: {output_path / 'instance_images'}")
    print(f"3. Run training with: python src/train_flux.py --config config/flux_lora_config.yaml")


def create_style_dataset(
    input_dir: str,
    output_dir: str,
    style_name: str,
    style_description: str = "artistic style",
    resize_images: bool = True,
    target_size: int = 1024,
):
    """
    Create FLUX style LoRA dataset
    
    Args:
        input_dir: Directory containing style images
        output_dir: Output directory for organized dataset
        style_name: Name of the style (e.g., "MYSTYLE")
        style_description: Description of the style
        resize_images: Whether to resize images
        target_size: Target size for resizing
    """
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    # Create output directories
    (output_path / "images").mkdir(parents=True, exist_ok=True)
    
    # Find all image files
    image_extensions = {'.jpg', '.jpeg', '.png', '.webp', '.bmp'}
    image_files = []
    
    for ext in image_extensions:
        image_files.extend(input_path.glob(f"*{ext}"))
        image_files.extend(input_path.glob(f"*{ext.upper()}"))
    
    print(f"Found {len(image_files)} style images")
    
    metadata = []
    valid_count = 0
    
    for i, image_file in enumerate(image_files):
        print(f"Processing {i+1}/{len(image_files)}: {image_file.name}")
        
        # Validate image
        image_info = validate_image(str(image_file))
        
        if not image_info["valid"]:
            print(f"  Skipping: {image_info['error']}")
            continue
        
        # Determine output filename
        output_filename = f"style_{valid_count:06d}.jpg"
        output_image_path = output_path / "images" / output_filename
        
        # Resize and copy image
        if resize_images:
            resize_image(str(image_file), str(output_image_path), target_size)
        else:
            shutil.copy2(image_file, output_image_path)
        
        # Create style-specific caption
        base_description = image_file.stem.replace('_', ' ').replace('-', ' ')
        caption = f"A {style_description} image of {base_description} in {style_name} style"
        
        # Create metadata entry
        metadata_entry = {
            "image": f"images/{output_filename}",
            "caption": caption,
            "style_name": style_name,
            "original_path": str(image_file),
        }
        
        metadata.append(metadata_entry)
        valid_count += 1
        
        print(f"  Added with caption: '{caption}'")
    
    # Save metadata
    metadata_path = output_path / "metadata.json"
    with open(metadata_path, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print(f"\nStyle dataset creation complete!")
    print(f"Style images: {valid_count}")
    print(f"Style name: {style_name}")
    print(f"Output directory: {output_path}")
    print(f"Metadata saved to: {metadata_path}")
    print(f"\nAt inference time, use prompts like:")
    print(f"  'A portrait in {style_name} style'")
    print(f"  'A landscape painting in {style_name} style'")


def main():
    parser = argparse.ArgumentParser(description="Prepare dataset for FLUX.1-dev finetuning")
    parser.add_argument(
        "--mode",
        type=str,
        choices=["text2image", "dreambooth", "style"],
        required=True,
        help="Dataset preparation mode"
    )
    parser.add_argument(
        "--input_dir",
        type=str,
        required=True,
        help="Input directory containing images"
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        required=True,
        help="Output directory for organized dataset"
    )
    parser.add_argument(
        "--resize_images",
        action="store_true",
        help="Resize images to target size"
    )
    parser.add_argument(
        "--target_size",
        type=int,
        default=1024,
        help="Target size for resizing (default: 1024)"
    )
    parser.add_argument(
        "--min_size",
        type=int,
        default=512,
        help="Minimum image size to accept (default: 512)"
    )
    parser.add_argument(
        "--max_samples",
        type=int,
        default=None,
        help="Maximum number of samples to process"
    )
    
    # Dreambooth specific arguments
    parser.add_argument(
        "--instance_prompt",
        type=str,
        default="a photo of sks person",
        help="Instance prompt for Dreambooth (default: 'a photo of sks person')"
    )
    parser.add_argument(
        "--class_prompt",
        type=str,
        default="a photo of person",
        help="Class prompt for Dreambooth (default: 'a photo of person')"
    )
    
    # Style specific arguments
    parser.add_argument(
        "--style_name",
        type=str,
        default="MYSTYLE",
        help="Style name for style LoRA (default: 'MYSTYLE')"
    )
    parser.add_argument(
        "--style_description",
        type=str,
        default="artistic style",
        help="Style description (default: 'artistic style')"
    )
    
    args = parser.parse_args()
    
    if args.mode == "text2image":
        create_text_to_image_dataset(
            input_dir=args.input_dir,
            output_dir=args.output_dir,
            resize_images=args.resize_images,
            target_size=args.target_size,
            min_size=args.min_size,
            max_samples=args.max_samples,
        )
    elif args.mode == "dreambooth":
        create_dreambooth_dataset(
            input_dir=args.input_dir,
            output_dir=args.output_dir,
            instance_prompt=args.instance_prompt,
            class_prompt=args.class_prompt,
            resize_images=args.resize_images,
            target_size=args.target_size,
        )
    elif args.mode == "style":
        create_style_dataset(
            input_dir=args.input_dir,
            output_dir=args.output_dir,
            style_name=args.style_name,
            style_description=args.style_description,
            resize_images=args.resize_images,
            target_size=args.target_size,
        )


if __name__ == "__main__":
    main()
