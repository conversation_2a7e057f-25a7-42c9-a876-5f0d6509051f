# Diffusion Model Finetuning Toolkit Requirements

# Core ML Libraries
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# Diffusion Models
diffusers>=0.24.0
transformers>=4.35.0
accelerate>=0.24.0

# LoRA/QLoRA/DoRA
peft>=0.6.0
bitsandbytes>=0.41.0

# Image/Video Processing
Pillow>=9.5.0
opencv-python>=4.8.0
decord>=0.6.0
imageio>=2.31.0
imageio-ffmpeg>=0.4.8

# Auto-Captioning
sentence-transformers>=2.2.0

# Data Processing
numpy>=1.24.0
pandas>=2.0.0
datasets>=2.14.0

# Configuration
omegaconf>=2.3.0
pyyaml>=6.0

# Monitoring & Logging
wandb>=0.15.0
tensorboard>=2.13.0
tqdm>=4.65.0

# Utilities
requests>=2.31.0
huggingface-hub>=0.17.0
safetensors>=0.3.0

# Development
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0

# Optional: Web Interface (if using the original app.py)
flask>=2.3.0
flask-socketio>=5.3.0
werkzeug>=2.3.0

# Optional: Jupyter Notebooks
jupyter>=1.0.0
ipywidgets>=8.0.0
