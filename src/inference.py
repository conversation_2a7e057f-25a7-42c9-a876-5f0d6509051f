"""
Inference script for finetuned Wan2.1 Image-to-Video model
"""

import os
import argparse
from pathlib import Path
from typing import Optional

import torch
import numpy as np
from PIL import Image
from diffusers import AutoencoderKLWan, WanImageToVideoPipeline
from diffusers.utils import export_to_video, load_image
from transformers import CLIPVisionModel
from peft import PeftModel

def load_finetuned_pipeline(
    base_model_path: str,
    lora_path: Optional[str] = None,
    device: str = "cuda",
    torch_dtype = torch.bfloat16,
):
    """Load finetuned Wan2.1 pipeline"""
    
    # Load base components
    image_encoder = CLIPVisionModel.from_pretrained(
        base_model_path, 
        subfolder="image_encoder", 
        torch_dtype=torch.float32
    )
    
    vae = AutoencoderKLWan.from_pretrained(
        base_model_path, 
        subfolder="vae", 
        torch_dtype=torch.float32
    )
    
    # Load transformer
    try:
        from diffusers.models import WanTransformer3DModel
    except ImportError:
        from diffusers import WanTransformer3DModel
        
    transformer = WanTransformer3DModel.from_pretrained(
        base_model_path,
        subfolder="transformer",
        torch_dtype=torch_dtype,
    )
    
    # Load LoRA weights if provided
    if lora_path and os.path.exists(lora_path):
        print(f"Loading LoRA weights from {lora_path}")
        transformer = PeftModel.from_pretrained(transformer, lora_path)
        transformer = transformer.merge_and_unload()  # Merge LoRA weights
    
    # Create pipeline
    pipe = WanImageToVideoPipeline.from_pretrained(
        base_model_path,
        vae=vae,
        image_encoder=image_encoder,
        transformer=transformer,
        torch_dtype=torch_dtype
    )
    
    pipe.to(device)
    return pipe


def generate_video(
    pipeline: WanImageToVideoPipeline,
    image_path: str,
    prompt: str,
    negative_prompt: str = "",
    output_path: str = "output.mp4",
    width: int = 1280,
    height: int = 720,
    num_frames: int = 81,
    guidance_scale: float = 5.0,
    num_inference_steps: int = 50,
    fps: int = 16,
    seed: Optional[int] = None,
):
    """Generate video from image and prompt"""
    
    # Load and preprocess image
    image = load_image(image_path)
    
    # Calculate optimal dimensions maintaining aspect ratio
    max_area = height * width
    aspect_ratio = image.height / image.width
    mod_value = pipeline.vae_scale_factor_spatial * pipeline.transformer.config.patch_size[1]
    
    calc_height = round(np.sqrt(max_area * aspect_ratio)) // mod_value * mod_value
    calc_width = round(np.sqrt(max_area / aspect_ratio)) // mod_value * mod_value
    
    image = image.resize((calc_width, calc_height))
    
    # Setup generator for reproducibility
    generator = None
    if seed is not None:
        generator = torch.Generator(device=pipeline.device).manual_seed(seed)
    
    print(f"Generating video...")
    print(f"Image size: {calc_width}x{calc_height}")
    print(f"Prompt: {prompt}")
    print(f"Negative prompt: {negative_prompt}")
    print(f"Frames: {num_frames}, FPS: {fps}")
    
    # Generate video
    with torch.no_grad():
        result = pipeline(
            image=image,
            prompt=prompt,
            negative_prompt=negative_prompt,
            height=calc_height,
            width=calc_width,
            num_frames=num_frames,
            guidance_scale=guidance_scale,
            num_inference_steps=num_inference_steps,
            generator=generator,
        )
    
    # Export video
    export_to_video(result.frames[0], output_path, fps=fps)
    print(f"Video saved to: {output_path}")
    
    return output_path


def main():
    parser = argparse.ArgumentParser(description="Generate video with finetuned Wan2.1 model")
    parser.add_argument(
        "--base_model_path",
        type=str,
        default="Wan-AI/Wan2.1-I2V-14B-720P-Diffusers",
        help="Path to base Wan2.1 model"
    )
    parser.add_argument(
        "--lora_path",
        type=str,
        default=None,
        help="Path to LoRA weights directory"
    )
    parser.add_argument(
        "--image_path",
        type=str,
        required=True,
        help="Path to input image"
    )
    parser.add_argument(
        "--prompt",
        type=str,
        required=True,
        help="Text prompt for video generation"
    )
    parser.add_argument(
        "--negative_prompt",
        type=str,
        default="blurry, low quality, distorted, static, ugly, deformed",
        help="Negative prompt"
    )
    parser.add_argument(
        "--output_path",
        type=str,
        default="generated_video.mp4",
        help="Output video path"
    )
    parser.add_argument(
        "--width",
        type=int,
        default=1280,
        help="Video width"
    )
    parser.add_argument(
        "--height",
        type=int,
        default=720,
        help="Video height"
    )
    parser.add_argument(
        "--num_frames",
        type=int,
        default=81,
        help="Number of frames to generate"
    )
    parser.add_argument(
        "--guidance_scale",
        type=float,
        default=5.0,
        help="Guidance scale for generation"
    )
    parser.add_argument(
        "--num_inference_steps",
        type=int,
        default=50,
        help="Number of inference steps"
    )
    parser.add_argument(
        "--fps",
        type=int,
        default=16,
        help="Output video FPS"
    )
    parser.add_argument(
        "--seed",
        type=int,
        default=None,
        help="Random seed for reproducibility"
    )
    parser.add_argument(
        "--device",
        type=str,
        default="cuda",
        help="Device to use for inference"
    )
    
    args = parser.parse_args()
    
    # Check if input image exists
    if not os.path.exists(args.image_path):
        raise FileNotFoundError(f"Input image not found: {args.image_path}")
    
    # Load pipeline
    print("Loading pipeline...")
    pipeline = load_finetuned_pipeline(
        base_model_path=args.base_model_path,
        lora_path=args.lora_path,
        device=args.device,
    )
    
    # Generate video
    output_path = generate_video(
        pipeline=pipeline,
        image_path=args.image_path,
        prompt=args.prompt,
        negative_prompt=args.negative_prompt,
        output_path=args.output_path,
        width=args.width,
        height=args.height,
        num_frames=args.num_frames,
        guidance_scale=args.guidance_scale,
        num_inference_steps=args.num_inference_steps,
        fps=args.fps,
        seed=args.seed,
    )
    
    print(f"Generation complete! Video saved to: {output_path}")


if __name__ == "__main__":
    main()
