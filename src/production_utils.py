"""
Production utilities for monitoring, logging, and error handling
"""

import os
import sys
import time
import json
import logging
import traceback
import psutil
import threading
from pathlib import Path
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timedelta
from contextlib import contextmanager

import torch
import numpy as np


class ProductionLogger:
    """Enhanced logging for production environments"""
    
    def __init__(self, name: str, log_dir: str = "./logs", level: str = "INFO"):
        self.name = name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper()))
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # File handler
        log_file = self.log_dir / f"{name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        file_handler = logging.FileHandler(log_file)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)
        
        # Error file handler
        error_file = self.log_dir / f"{name}_errors_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        error_handler = logging.FileHandler(error_file)
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        self.logger.addHandler(error_handler)
        
    def info(self, message: str, **kwargs):
        """Log info message with optional structured data"""
        if kwargs:
            message = f"{message} | {json.dumps(kwargs)}"
        self.logger.info(message)
        
    def warning(self, message: str, **kwargs):
        """Log warning message with optional structured data"""
        if kwargs:
            message = f"{message} | {json.dumps(kwargs)}"
        self.logger.warning(message)
        
    def error(self, message: str, exception: Optional[Exception] = None, **kwargs):
        """Log error message with optional exception and structured data"""
        if exception:
            message = f"{message} | Exception: {str(exception)}"
        if kwargs:
            message = f"{message} | {json.dumps(kwargs)}"
        self.logger.error(message)
        
        if exception:
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            
    def debug(self, message: str, **kwargs):
        """Log debug message with optional structured data"""
        if kwargs:
            message = f"{message} | {json.dumps(kwargs)}"
        self.logger.debug(message)


class SystemMonitor:
    """System resource monitoring for production training"""
    
    def __init__(self, log_interval: int = 60):
        self.log_interval = log_interval
        self.monitoring = False
        self.monitor_thread = None
        self.logger = ProductionLogger("SystemMonitor")
        
    def get_system_stats(self) -> Dict[str, Any]:
        """Get current system statistics"""
        stats = {
            "timestamp": datetime.now().isoformat(),
            "cpu": {
                "percent": psutil.cpu_percent(interval=1),
                "count": psutil.cpu_count(),
                "freq": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
            },
            "memory": {
                "total_gb": psutil.virtual_memory().total / (1024**3),
                "available_gb": psutil.virtual_memory().available / (1024**3),
                "used_gb": psutil.virtual_memory().used / (1024**3),
                "percent": psutil.virtual_memory().percent,
            },
            "disk": {
                "total_gb": psutil.disk_usage('/').total / (1024**3),
                "free_gb": psutil.disk_usage('/').free / (1024**3),
                "used_gb": psutil.disk_usage('/').used / (1024**3),
                "percent": psutil.disk_usage('/').percent,
            }
        }
        
        # GPU stats if available
        if torch.cuda.is_available():
            gpu_stats = []
            for i in range(torch.cuda.device_count()):
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
                gpu_allocated = torch.cuda.memory_allocated(i) / (1024**3)
                gpu_reserved = torch.cuda.memory_reserved(i) / (1024**3)
                
                gpu_stats.append({
                    "device": i,
                    "name": torch.cuda.get_device_name(i),
                    "total_memory_gb": gpu_memory,
                    "allocated_memory_gb": gpu_allocated,
                    "reserved_memory_gb": gpu_reserved,
                    "utilization_percent": (gpu_allocated / gpu_memory) * 100,
                })
            stats["gpu"] = gpu_stats
            
        return stats
        
    def _monitor_loop(self):
        """Background monitoring loop"""
        while self.monitoring:
            try:
                stats = self.get_system_stats()
                
                # Log warnings for high resource usage
                if stats["memory"]["percent"] > 90:
                    self.logger.warning("High memory usage detected", memory_percent=stats["memory"]["percent"])
                    
                if stats["disk"]["percent"] > 90:
                    self.logger.warning("High disk usage detected", disk_percent=stats["disk"]["percent"])
                    
                if "gpu" in stats:
                    for gpu in stats["gpu"]:
                        if gpu["utilization_percent"] > 95:
                            self.logger.warning("High GPU memory usage detected", 
                                              gpu_device=gpu["device"], 
                                              utilization=gpu["utilization_percent"])
                
                # Log stats periodically
                self.logger.debug("System stats", **stats)
                
            except Exception as e:
                self.logger.error("Error in monitoring loop", exception=e)
                
            time.sleep(self.log_interval)
            
    def start_monitoring(self):
        """Start background system monitoring"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            self.logger.info("System monitoring started")
            
    def stop_monitoring(self):
        """Stop background system monitoring"""
        if self.monitoring:
            self.monitoring = False
            if self.monitor_thread:
                self.monitor_thread.join(timeout=5)
            self.logger.info("System monitoring stopped")


class TrainingMonitor:
    """Training-specific monitoring and metrics"""
    
    def __init__(self, log_dir: str = "./logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        self.logger = ProductionLogger("TrainingMonitor")
        
        self.start_time = None
        self.step_times = []
        self.losses = []
        self.learning_rates = []
        
    def start_training(self):
        """Mark training start"""
        self.start_time = time.time()
        self.logger.info("Training started")
        
    def log_step(self, step: int, loss: float, lr: float, **kwargs):
        """Log training step metrics"""
        current_time = time.time()
        
        if self.start_time:
            elapsed = current_time - self.start_time
            self.step_times.append(elapsed)
            
        self.losses.append(loss)
        self.learning_rates.append(lr)
        
        # Calculate moving averages
        window_size = min(100, len(self.losses))
        avg_loss = np.mean(self.losses[-window_size:])
        
        # Estimate time remaining
        if len(self.step_times) > 10:
            avg_step_time = np.mean(np.diff(self.step_times[-10:]))
            steps_remaining = kwargs.get("max_steps", 1000) - step
            eta_seconds = avg_step_time * steps_remaining
            eta = str(timedelta(seconds=int(eta_seconds)))
        else:
            eta = "Unknown"
            
        self.logger.info(
            f"Step {step}",
            loss=loss,
            avg_loss=avg_loss,
            learning_rate=lr,
            eta=eta,
            **kwargs
        )
        
    def log_validation(self, step: int, metrics: Dict[str, Any]):
        """Log validation metrics"""
        self.logger.info(f"Validation at step {step}", **metrics)
        
    def log_checkpoint(self, step: int, checkpoint_path: str):
        """Log checkpoint save"""
        self.logger.info(f"Checkpoint saved at step {step}", path=checkpoint_path)
        
    def get_training_summary(self) -> Dict[str, Any]:
        """Get training summary statistics"""
        if not self.losses:
            return {}
            
        total_time = self.step_times[-1] - self.step_times[0] if len(self.step_times) > 1 else 0
        
        return {
            "total_steps": len(self.losses),
            "total_time_seconds": total_time,
            "total_time_formatted": str(timedelta(seconds=int(total_time))),
            "final_loss": self.losses[-1],
            "min_loss": min(self.losses),
            "avg_loss": np.mean(self.losses),
            "final_lr": self.learning_rates[-1] if self.learning_rates else 0,
        }


class ErrorHandler:
    """Production error handling and recovery"""
    
    def __init__(self, log_dir: str = "./logs"):
        self.logger = ProductionLogger("ErrorHandler", log_dir)
        
    @contextmanager
    def error_context(self, operation: str):
        """Context manager for error handling"""
        try:
            self.logger.info(f"Starting operation: {operation}")
            yield
            self.logger.info(f"Completed operation: {operation}")
        except Exception as e:
            self.logger.error(f"Error in operation: {operation}", exception=e)
            raise
            
    def handle_cuda_oom(self, operation: str = "training"):
        """Handle CUDA out of memory errors"""
        self.logger.warning(f"CUDA OOM detected during {operation}")
        
        if torch.cuda.is_available():
            # Clear cache
            torch.cuda.empty_cache()
            
            # Log memory stats
            for i in range(torch.cuda.device_count()):
                allocated = torch.cuda.memory_allocated(i) / (1024**3)
                reserved = torch.cuda.memory_reserved(i) / (1024**3)
                self.logger.info(f"GPU {i} memory after cleanup", 
                               allocated_gb=allocated, 
                               reserved_gb=reserved)
                               
        self.logger.info("CUDA memory cleanup completed")
        
    def safe_save_checkpoint(self, model, path: str, max_retries: int = 3):
        """Safely save checkpoint with retries"""
        for attempt in range(max_retries):
            try:
                model.save_pretrained(path)
                self.logger.info(f"Checkpoint saved successfully", path=path, attempt=attempt+1)
                return True
            except Exception as e:
                self.logger.warning(f"Checkpoint save attempt {attempt+1} failed", 
                                  path=path, exception=str(e))
                if attempt < max_retries - 1:
                    time.sleep(5)  # Wait before retry
                    
        self.logger.error(f"Failed to save checkpoint after {max_retries} attempts", path=path)
        return False


class NotificationManager:
    """Send notifications about training progress"""
    
    def __init__(self, config_manager=None):
        self.config_manager = config_manager
        self.logger = ProductionLogger("NotificationManager")
        
    def send_slack_notification(self, message: str, webhook_url: Optional[str] = None):
        """Send Slack notification"""
        if not webhook_url and self.config_manager:
            webhook_url = self.config_manager.get("SLACK_WEBHOOK_URL")
            
        if not webhook_url or webhook_url == "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK":
            return False
            
        try:
            import requests
            
            payload = {"text": message}
            response = requests.post(webhook_url, json=payload, timeout=10)
            response.raise_for_status()
            
            self.logger.info("Slack notification sent successfully")
            return True
            
        except Exception as e:
            self.logger.error("Failed to send Slack notification", exception=e)
            return False
            
    def send_email_notification(self, subject: str, message: str):
        """Send email notification"""
        if not self.config_manager:
            return False
            
        smtp_server = self.config_manager.get("SMTP_SERVER")
        smtp_port = self.config_manager.get("SMTP_PORT")
        smtp_username = self.config_manager.get("SMTP_USERNAME")
        smtp_password = self.config_manager.get("SMTP_PASSWORD")
        notification_email = self.config_manager.get("NOTIFICATION_EMAIL")
        
        if not all([smtp_server, smtp_username, smtp_password, notification_email]):
            return False
            
        try:
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            
            msg = MIMEMultipart()
            msg['From'] = smtp_username
            msg['To'] = notification_email
            msg['Subject'] = subject
            
            msg.attach(MIMEText(message, 'plain'))
            
            server = smtplib.SMTP(smtp_server, int(smtp_port))
            server.starttls()
            server.login(smtp_username, smtp_password)
            server.send_message(msg)
            server.quit()
            
            self.logger.info("Email notification sent successfully")
            return True
            
        except Exception as e:
            self.logger.error("Failed to send email notification", exception=e)
            return False
            
    def notify_training_start(self, config: Dict[str, Any]):
        """Notify training start"""
        model_name = config.get("model", {}).get("name", "Unknown")
        message = f"🚀 Training started for model: {model_name}"
        
        self.send_slack_notification(message)
        self.send_email_notification("Training Started", message)
        
    def notify_training_complete(self, summary: Dict[str, Any]):
        """Notify training completion"""
        message = f"✅ Training completed!\n"
        message += f"Total steps: {summary.get('total_steps', 'Unknown')}\n"
        message += f"Total time: {summary.get('total_time_formatted', 'Unknown')}\n"
        message += f"Final loss: {summary.get('final_loss', 'Unknown'):.4f}"
        
        self.send_slack_notification(message)
        self.send_email_notification("Training Completed", message)
        
    def notify_error(self, error_message: str):
        """Notify about errors"""
        message = f"❌ Training error: {error_message}"
        
        self.send_slack_notification(message)
        self.send_email_notification("Training Error", message)


def setup_production_environment(config_manager=None):
    """Setup complete production environment"""
    print("🏭 Setting up production environment...")
    
    # Setup system monitoring
    system_monitor = SystemMonitor()
    system_monitor.start_monitoring()
    
    # Setup error handling
    error_handler = ErrorHandler()
    
    # Setup notifications
    notification_manager = NotificationManager(config_manager)
    
    print("✅ Production environment setup complete!")
    
    return {
        "system_monitor": system_monitor,
        "error_handler": error_handler,
        "notification_manager": notification_manager,
    }


if __name__ == "__main__":
    # Test production utilities
    from config_manager import get_config_manager
    
    config_mgr = get_config_manager()
    prod_env = setup_production_environment(config_mgr)
    
    # Test system stats
    monitor = prod_env["system_monitor"]
    stats = monitor.get_system_stats()
    print(f"System stats: {json.dumps(stats, indent=2)}")
    
    # Test error handling
    error_handler = prod_env["error_handler"]
    with error_handler.error_context("test_operation"):
        print("Test operation completed successfully")
        
    # Cleanup
    monitor.stop_monitoring()
