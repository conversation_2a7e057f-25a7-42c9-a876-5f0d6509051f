"""
Authentication and security manager for production deployment
Handles secure token management, API authentication, and security validation
"""

import os
import sys
import logging
import hashlib
import base64
from pathlib import Path
from typing import Optional, Dict, Any, List
import warnings

import requests
from huggingface_hub import HfA<PERSON>, login, logout
from huggingface_hub.utils import HfHubHTTPError


class AuthManager:
    """
    Production-ready authentication manager for secure API access
    """
    
    def __init__(self, config_manager=None):
        """Initialize authentication manager"""
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        self.hf_api = None
        
    def validate_hf_token(self, token: str) -> Dict[str, Any]:
        """
        Validate Hugging Face token and get user info
        
        Args:
            token: Hugging Face token to validate
            
        Returns:
            Dictionary with validation results and user info
        """
        if not token or token == "your_huggingface_token_here":
            return {
                "valid": False,
                "error": "No token provided or using placeholder token",
                "user_info": None
            }
            
        try:
            # Initialize HF API with token
            api = HfApi(token=token)
            
            # Get user info to validate token
            user_info = api.whoami()
            
            return {
                "valid": True,
                "error": None,
                "user_info": user_info,
                "token_type": user_info.get("type", "unknown"),
                "username": user_info.get("name", "unknown")
            }
            
        except HfHubHTTPError as e:
            if e.response.status_code == 401:
                return {
                    "valid": False,
                    "error": "Invalid or expired token",
                    "user_info": None
                }
            else:
                return {
                    "valid": False,
                    "error": f"HTTP error: {e.response.status_code}",
                    "user_info": None
                }
        except Exception as e:
            return {
                "valid": False,
                "error": f"Validation error: {str(e)}",
                "user_info": None
            }
            
    def setup_hf_authentication(self, token: Optional[str] = None) -> bool:
        """
        Setup Hugging Face authentication
        
        Args:
            token: Optional token override
            
        Returns:
            True if authentication successful, False otherwise
        """
        if not token and self.config_manager:
            token = self.config_manager.get_hf_token()
            
        if not token:
            self.logger.warning("No Hugging Face token available")
            return False
            
        # Validate token
        validation_result = self.validate_hf_token(token)
        
        if not validation_result["valid"]:
            self.logger.error(f"HF token validation failed: {validation_result['error']}")
            return False
            
        try:
            # Login to Hugging Face Hub
            login(token=token, add_to_git_credential=False)
            
            # Initialize API
            self.hf_api = HfApi(token=token)
            
            user_info = validation_result["user_info"]
            self.logger.info(f"✅ Authenticated with Hugging Face as: {user_info.get('name', 'unknown')}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to setup HF authentication: {e}")
            return False
            
    def check_model_access(self, model_id: str) -> Dict[str, Any]:
        """
        Check if we have access to a specific model
        
        Args:
            model_id: Model identifier (e.g., "black-forest-labs/FLUX.1-dev")
            
        Returns:
            Dictionary with access information
        """
        if not self.hf_api:
            return {
                "accessible": False,
                "error": "Not authenticated with Hugging Face",
                "model_info": None
            }
            
        try:
            # Get model info
            model_info = self.hf_api.model_info(model_id)
            
            return {
                "accessible": True,
                "error": None,
                "model_info": {
                    "id": model_info.id,
                    "private": model_info.private,
                    "gated": getattr(model_info, 'gated', False),
                    "downloads": getattr(model_info, 'downloads', 0),
                    "likes": getattr(model_info, 'likes', 0),
                    "library_name": getattr(model_info, 'library_name', 'unknown'),
                    "tags": getattr(model_info, 'tags', [])
                }
            }
            
        except HfHubHTTPError as e:
            if e.response.status_code == 401:
                return {
                    "accessible": False,
                    "error": "Authentication required for this model",
                    "model_info": None
                }
            elif e.response.status_code == 403:
                return {
                    "accessible": False,
                    "error": "Access denied - you may need to accept the model's license",
                    "model_info": None
                }
            elif e.response.status_code == 404:
                return {
                    "accessible": False,
                    "error": "Model not found",
                    "model_info": None
                }
            else:
                return {
                    "accessible": False,
                    "error": f"HTTP error {e.response.status_code}: {e}",
                    "model_info": None
                }
        except Exception as e:
            return {
                "accessible": False,
                "error": f"Error checking model access: {str(e)}",
                "model_info": None
            }
            
    def validate_wandb_token(self, api_key: str) -> Dict[str, Any]:
        """
        Validate Weights & Biases API key
        
        Args:
            api_key: W&B API key to validate
            
        Returns:
            Dictionary with validation results
        """
        if not api_key or api_key == "your_wandb_api_key_here":
            return {
                "valid": False,
                "error": "No API key provided or using placeholder",
                "user_info": None
            }
            
        try:
            import wandb
            
            # Test API key by initializing wandb
            wandb.login(key=api_key, verify=True)
            
            # Get user info
            api = wandb.Api(api_key=api_key)
            user = api.viewer
            
            return {
                "valid": True,
                "error": None,
                "user_info": {
                    "username": user.username,
                    "email": getattr(user, 'email', 'unknown'),
                    "teams": [team.name for team in user.teams] if hasattr(user, 'teams') else []
                }
            }
            
        except Exception as e:
            return {
                "valid": False,
                "error": f"W&B validation error: {str(e)}",
                "user_info": None
            }
            
    def setup_wandb_authentication(self, api_key: Optional[str] = None) -> bool:
        """
        Setup Weights & Biases authentication
        
        Args:
            api_key: Optional API key override
            
        Returns:
            True if authentication successful, False otherwise
        """
        try:
            import wandb
        except ImportError:
            self.logger.warning("wandb not installed, skipping W&B authentication")
            return False
            
        if not api_key and self.config_manager:
            wandb_config = self.config_manager.get_wandb_config()
            api_key = wandb_config.get("api_key")
            
        if not api_key or api_key == "your_wandb_api_key_here":
            self.logger.info("No W&B API key provided, skipping authentication")
            return False
            
        # Validate API key
        validation_result = self.validate_wandb_token(api_key)
        
        if not validation_result["valid"]:
            self.logger.error(f"W&B API key validation failed: {validation_result['error']}")
            return False
            
        try:
            # Login to W&B
            wandb.login(key=api_key)
            
            user_info = validation_result["user_info"]
            self.logger.info(f"✅ Authenticated with W&B as: {user_info.get('username', 'unknown')}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to setup W&B authentication: {e}")
            return False
            
    def check_required_models(self, model_list: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Check access to multiple required models
        
        Args:
            model_list: List of model IDs to check
            
        Returns:
            Dictionary mapping model IDs to access information
        """
        results = {}
        
        for model_id in model_list:
            self.logger.info(f"Checking access to model: {model_id}")
            results[model_id] = self.check_model_access(model_id)
            
        return results
        
    def generate_secure_run_name(self, prefix: str = "run") -> str:
        """
        Generate a secure, unique run name for experiments
        
        Args:
            prefix: Prefix for the run name
            
        Returns:
            Unique run name
        """
        import time
        import random
        
        timestamp = int(time.time())
        random_suffix = random.randint(1000, 9999)
        
        return f"{prefix}_{timestamp}_{random_suffix}"
        
    def mask_sensitive_info(self, text: str) -> str:
        """
        Mask sensitive information in logs/output
        
        Args:
            text: Text that may contain sensitive information
            
        Returns:
            Text with sensitive information masked
        """
        # Mask HF tokens
        import re
        
        # Mask HF tokens (hf_xxxx...)
        text = re.sub(r'hf_[a-zA-Z0-9]{34}', 'hf_****', text)
        
        # Mask W&B API keys (typically 40 chars)
        text = re.sub(r'\b[a-f0-9]{40}\b', '****', text)
        
        # Mask other potential tokens/keys
        text = re.sub(r'sk-[a-zA-Z0-9]{48}', 'sk-****', text)  # OpenAI-style keys
        text = re.sub(r'AIza[a-zA-Z0-9]{35}', 'AIza****', text)  # Google API keys
        
        return text
        
    def setup_all_authentication(self) -> Dict[str, bool]:
        """
        Setup all authentication services
        
        Returns:
            Dictionary with authentication results for each service
        """
        results = {}
        
        self.logger.info("🔐 Setting up authentication...")
        
        # Setup Hugging Face
        results["huggingface"] = self.setup_hf_authentication()
        
        # Setup Weights & Biases
        results["wandb"] = self.setup_wandb_authentication()
        
        # Summary
        successful = sum(results.values())
        total = len(results)
        
        if successful == total:
            self.logger.info(f"✅ All authentication services setup successfully ({successful}/{total})")
        else:
            self.logger.warning(f"⚠️  Partial authentication setup ({successful}/{total} successful)")
            
        return results
        
    def print_authentication_status(self):
        """Print current authentication status"""
        print("\n" + "="*60)
        print("🔐 AUTHENTICATION STATUS")
        print("="*60)
        
        # Check HF authentication
        hf_token = self.config_manager.get_hf_token() if self.config_manager else None
        if hf_token:
            validation = self.validate_hf_token(hf_token)
            if validation["valid"]:
                user_info = validation["user_info"]
                print(f"Hugging Face: ✅ Authenticated as {user_info.get('name', 'unknown')}")
            else:
                print(f"Hugging Face: ❌ {validation['error']}")
        else:
            print("Hugging Face: ❌ No token configured")
            
        # Check W&B authentication
        try:
            import wandb
            if wandb.api.api_key:
                print(f"Weights & Biases: ✅ Authenticated")
            else:
                print("Weights & Biases: ❌ Not authenticated")
        except:
            print("Weights & Biases: ❌ Not available")
            
        print("="*60)


def create_auth_manager(config_manager=None) -> AuthManager:
    """Create and setup authentication manager"""
    auth_manager = AuthManager(config_manager)
    auth_manager.setup_all_authentication()
    return auth_manager


if __name__ == "__main__":
    # Test authentication manager
    from config_manager import get_config_manager
    
    config_mgr = get_config_manager()
    auth_mgr = create_auth_manager(config_mgr)
    
    auth_mgr.print_authentication_status()
    
    # Test model access
    models_to_check = [
        "Wan-AI/Wan2.1-I2V-14B-720P-Diffusers",
        "black-forest-labs/FLUX.1-dev"
    ]
    
    print("\n🔍 Checking model access...")
    results = auth_mgr.check_required_models(models_to_check)
    
    for model_id, result in results.items():
        status = "✅" if result["accessible"] else "❌"
        print(f"{status} {model_id}: {result.get('error', 'Accessible')}")
