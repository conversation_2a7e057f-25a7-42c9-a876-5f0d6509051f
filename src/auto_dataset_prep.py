"""
Automatic dataset preparation for FLUX training
Takes raw images and automatically generates captions, metadata, and dataset structure
"""

import os
import sys
import json
import logging
import argparse
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

import torch
from PIL import Image, ImageOps, ExifTags
import numpy as np
from tqdm.auto import tqdm

from auto_caption import AutoCaptioner, get_available_models
from config_manager import get_config_manager


class AutoDatasetPreparator:
    """
    Automatic dataset preparation system for FLUX training
    """
    
    def __init__(
        self,
        input_dir: str,
        output_dir: str,
        captioning_model: str = "blip2",
        max_images: Optional[int] = None,
        min_resolution: int = 512,
        max_resolution: int = 2048,
        quality_threshold: float = 0.7,
        batch_size: int = 4,
        num_workers: int = 4,
        enhance_captions: bool = True,
        copy_images: bool = True,
        image_formats: List[str] = None,
        cache_dir: Optional[str] = None
    ):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.captioning_model = captioning_model
        self.max_images = max_images
        self.min_resolution = min_resolution
        self.max_resolution = max_resolution
        self.quality_threshold = quality_threshold
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.enhance_captions = enhance_captions
        self.copy_images = copy_images
        self.cache_dir = cache_dir
        
        if image_formats is None:
            self.image_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
        else:
            self.image_formats = image_formats
            
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Initialize captioner
        self.captioner = None
        
        # Statistics
        self.stats = {
            "total_found": 0,
            "processed": 0,
            "skipped": 0,
            "errors": 0,
            "captions_generated": 0,
            "images_copied": 0
        }
        
    def setup_captioner(self):
        """Initialize the captioning model"""
        if self.captioner is None:
            self.logger.info(f"Initializing {self.captioning_model} captioner...")
            try:
                self.captioner = AutoCaptioner(
                    model_name=self.captioning_model,
                    batch_size=self.batch_size,
                    cache_dir=self.cache_dir
                )
                self.logger.info("Captioner initialized successfully")
            except Exception as e:
                self.logger.error(f"Failed to initialize captioner: {e}")
                raise
                
    def find_images(self) -> List[Path]:
        """Find all valid images in input directory"""
        self.logger.info(f"Scanning for images in {self.input_dir}")
        
        image_files = []
        
        for ext in self.image_formats:
            # Case insensitive search
            pattern = f"**/*{ext}"
            files = list(self.input_dir.glob(pattern))
            files.extend(list(self.input_dir.glob(pattern.upper())))
            image_files.extend(files)
            
        # Remove duplicates and sort
        image_files = sorted(list(set(image_files)))
        
        self.stats["total_found"] = len(image_files)
        self.logger.info(f"Found {len(image_files)} images")
        
        # Limit if specified
        if self.max_images and len(image_files) > self.max_images:
            image_files = image_files[:self.max_images]
            self.logger.info(f"Limited to {self.max_images} images")
            
        return image_files
        
    def analyze_image(self, image_path: Path) -> Dict[str, Any]:
        """
        Analyze image properties and quality
        
        Args:
            image_path: Path to image file
            
        Returns:
            Dictionary with image analysis results
        """
        try:
            with Image.open(image_path) as img:
                # Get basic properties
                width, height = img.size
                mode = img.mode
                format_name = img.format
                
                # Handle EXIF orientation
                try:
                    for orientation in ExifTags.TAGS.keys():
                        if ExifTags.TAGS[orientation] == 'Orientation':
                            break
                    exif = img._getexif()
                    if exif is not None:
                        orientation_value = exif.get(orientation)
                        if orientation_value in [3, 6, 8]:
                            # Image needs rotation
                            img = ImageOps.exif_transpose(img)
                            width, height = img.size
                except:
                    pass
                
                # Calculate quality metrics
                min_dim = min(width, height)
                max_dim = max(width, height)
                aspect_ratio = width / height
                
                # Quality score based on resolution and aspect ratio
                resolution_score = min(min_dim / self.min_resolution, 1.0)
                aspect_score = 1.0 if 0.5 <= aspect_ratio <= 2.0 else 0.5
                
                # Check if image is too small or too large
                too_small = min_dim < self.min_resolution
                too_large = max_dim > self.max_resolution
                
                # Overall quality score
                quality_score = (resolution_score + aspect_score) / 2
                
                return {
                    "width": width,
                    "height": height,
                    "mode": mode,
                    "format": format_name,
                    "aspect_ratio": aspect_ratio,
                    "min_dimension": min_dim,
                    "max_dimension": max_dim,
                    "quality_score": quality_score,
                    "too_small": too_small,
                    "too_large": too_large,
                    "valid": quality_score >= self.quality_threshold and not too_small,
                    "file_size": image_path.stat().st_size
                }
                
        except Exception as e:
            self.logger.error(f"Error analyzing {image_path}: {e}")
            return {
                "valid": False,
                "error": str(e)
            }
            
    def process_single_image(self, image_path: Path, output_images_dir: Path) -> Optional[Dict[str, Any]]:
        """
        Process a single image: analyze, copy, and prepare metadata
        
        Args:
            image_path: Path to source image
            output_images_dir: Directory to copy image to
            
        Returns:
            Metadata dictionary or None if processing failed
        """
        try:
            # Analyze image
            analysis = self.analyze_image(image_path)
            
            if not analysis.get("valid", False):
                self.stats["skipped"] += 1
                reason = analysis.get("error", "quality threshold not met")
                self.logger.debug(f"Skipping {image_path.name}: {reason}")
                return None
                
            # Generate output filename
            output_filename = f"image_{self.stats['processed']:06d}{image_path.suffix.lower()}"
            output_path = output_images_dir / output_filename
            
            # Copy image if requested
            if self.copy_images:
                # Resize if too large
                if analysis["too_large"]:
                    self._resize_image(image_path, output_path, self.max_resolution)
                else:
                    shutil.copy2(image_path, output_path)
                self.stats["images_copied"] += 1
            else:
                # Just create a relative path reference
                output_filename = str(image_path.relative_to(self.input_dir))
                
            # Prepare metadata
            metadata = {
                "image": f"images/{output_filename}",
                "original_path": str(image_path),
                "width": analysis["width"],
                "height": analysis["height"],
                "aspect_ratio": analysis["aspect_ratio"],
                "quality_score": analysis["quality_score"],
                "file_size": analysis["file_size"],
                "format": analysis["format"]
            }
            
            self.stats["processed"] += 1
            return metadata
            
        except Exception as e:
            self.stats["errors"] += 1
            self.logger.error(f"Error processing {image_path}: {e}")
            return None
            
    def _resize_image(self, input_path: Path, output_path: Path, max_size: int):
        """Resize image while maintaining aspect ratio"""
        with Image.open(input_path) as img:
            # Handle EXIF orientation
            img = ImageOps.exif_transpose(img)
            
            # Calculate new size
            width, height = img.size
            if max(width, height) > max_size:
                if width > height:
                    new_width = max_size
                    new_height = int(height * max_size / width)
                else:
                    new_height = max_size
                    new_width = int(width * max_size / height)
                    
                img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                
            # Save with high quality
            save_kwargs = {"quality": 95, "optimize": True}
            if img.mode == "RGBA":
                img = img.convert("RGB")
                
            img.save(output_path, **save_kwargs)
            
    def generate_captions(self, metadata_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Generate captions for all processed images
        
        Args:
            metadata_list: List of image metadata dictionaries
            
        Returns:
            Updated metadata list with captions
        """
        if not metadata_list:
            return metadata_list
            
        self.logger.info(f"Generating captions for {len(metadata_list)} images...")
        
        # Setup captioner
        self.setup_captioner()
        
        # Load images for captioning
        images = []
        valid_metadata = []
        
        for metadata in tqdm(metadata_list, desc="Loading images"):
            try:
                if self.copy_images:
                    image_path = self.output_dir / metadata["image"]
                else:
                    image_path = Path(metadata["original_path"])
                    
                with Image.open(image_path) as img:
                    # Convert to RGB if needed
                    if img.mode != "RGB":
                        img = img.convert("RGB")
                    images.append(img.copy())
                    valid_metadata.append(metadata)
                    
            except Exception as e:
                self.logger.error(f"Error loading image for captioning: {e}")
                continue
                
        if not images:
            self.logger.warning("No valid images found for captioning")
            return metadata_list
            
        # Generate captions in batches
        self.logger.info(f"Generating captions with {self.captioning_model}...")
        
        captions = []
        for i in tqdm(range(0, len(images), self.batch_size), desc="Captioning"):
            batch_images = images[i:i + self.batch_size]
            
            try:
                batch_captions = self.captioner.caption_batch(batch_images)
                captions.extend(batch_captions)
                self.stats["captions_generated"] += len(batch_captions)
                
            except Exception as e:
                self.logger.error(f"Error generating captions for batch {i}: {e}")
                # Add fallback captions
                fallback_captions = ["an image"] * len(batch_images)
                captions.extend(fallback_captions)
                
        # Enhance captions if requested
        if self.enhance_captions:
            self.logger.info("Enhancing captions...")
            enhanced_captions = []
            
            for caption, image, metadata in tqdm(zip(captions, images, valid_metadata), desc="Enhancing"):
                try:
                    enhanced = self.captioner.enhance_caption(caption, image)
                    enhanced_captions.append(enhanced)
                except Exception as e:
                    self.logger.error(f"Error enhancing caption: {e}")
                    enhanced_captions.append(caption)
                    
            captions = enhanced_captions
            
        # Add captions to metadata
        for metadata, caption in zip(valid_metadata, captions):
            metadata["caption"] = caption
            
        return valid_metadata
        
    def create_dataset_structure(self):
        """Create the output dataset directory structure"""
        self.logger.info(f"Creating dataset structure in {self.output_dir}")
        
        # Create directories
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        if self.copy_images:
            images_dir = self.output_dir / "images"
            images_dir.mkdir(exist_ok=True)
            return images_dir
        else:
            return None
            
    def save_metadata(self, metadata_list: List[Dict[str, Any]]):
        """Save metadata to JSON file"""
        metadata_path = self.output_dir / "metadata.json"
        
        self.logger.info(f"Saving metadata to {metadata_path}")
        
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata_list, f, indent=2, ensure_ascii=False)
            
    def save_statistics(self):
        """Save processing statistics"""
        stats_path = self.output_dir / "dataset_stats.json"
        
        # Add timing and model info
        self.stats.update({
            "captioning_model": self.captioning_model,
            "processing_time": time.time() - self.start_time,
            "input_directory": str(self.input_dir),
            "output_directory": str(self.output_dir),
            "settings": {
                "min_resolution": self.min_resolution,
                "max_resolution": self.max_resolution,
                "quality_threshold": self.quality_threshold,
                "enhance_captions": self.enhance_captions,
                "copy_images": self.copy_images
            }
        })
        
        with open(stats_path, 'w') as f:
            json.dump(self.stats, f, indent=2)
            
        self.logger.info(f"Statistics saved to {stats_path}")
        
    def prepare_dataset(self) -> Dict[str, Any]:
        """
        Main method to prepare the complete dataset
        
        Returns:
            Processing statistics
        """
        self.start_time = time.time()
        
        self.logger.info("Starting automatic dataset preparation...")
        
        # Create output structure
        output_images_dir = self.create_dataset_structure()
        
        # Find all images
        image_files = self.find_images()
        
        if not image_files:
            self.logger.error("No images found in input directory")
            return self.stats
            
        # Process images
        self.logger.info("Processing images...")
        metadata_list = []
        
        with ThreadPoolExecutor(max_workers=self.num_workers) as executor:
            # Submit all tasks
            future_to_path = {
                executor.submit(self.process_single_image, img_path, output_images_dir): img_path 
                for img_path in image_files
            }
            
            # Collect results
            for future in tqdm(as_completed(future_to_path), total=len(image_files), desc="Processing"):
                result = future.result()
                if result:
                    metadata_list.append(result)
                    
        if not metadata_list:
            self.logger.error("No images were successfully processed")
            return self.stats
            
        # Generate captions
        metadata_list = self.generate_captions(metadata_list)
        
        # Save metadata
        self.save_metadata(metadata_list)
        
        # Save statistics
        self.save_statistics()
        
        # Print summary
        self.print_summary()
        
        return self.stats
        
    def print_summary(self):
        """Print processing summary"""
        print("\n" + "="*60)
        print("📊 DATASET PREPARATION SUMMARY")
        print("="*60)
        print(f"Input Directory: {self.input_dir}")
        print(f"Output Directory: {self.output_dir}")
        print(f"Captioning Model: {self.captioning_model}")
        print()
        print(f"Images Found: {self.stats['total_found']}")
        print(f"Images Processed: {self.stats['processed']}")
        print(f"Images Skipped: {self.stats['skipped']}")
        print(f"Errors: {self.stats['errors']}")
        print(f"Captions Generated: {self.stats['captions_generated']}")
        
        if self.copy_images:
            print(f"Images Copied: {self.stats['images_copied']}")
            
        processing_time = time.time() - self.start_time
        print(f"Processing Time: {processing_time:.1f} seconds")
        
        if self.stats['processed'] > 0:
            print(f"Average Time per Image: {processing_time / self.stats['processed']:.2f} seconds")
            
        print("="*60)
        print("✅ Dataset preparation complete!")
        print(f"📁 Dataset ready at: {self.output_dir}")
        print(f"📄 Metadata file: {self.output_dir / 'metadata.json'}")
        print()
        print("🚀 Ready for FLUX training!")


def main():
    """Main function for command line usage"""
    parser = argparse.ArgumentParser(description="Automatic FLUX dataset preparation")
    
    parser.add_argument("--input_dir", type=str, required=True, help="Input directory with images")
    parser.add_argument("--output_dir", type=str, required=True, help="Output directory for dataset")
    parser.add_argument("--captioning_model", type=str, default="blip2", 
                       choices=get_available_models(), help="Captioning model to use")
    parser.add_argument("--max_images", type=int, help="Maximum number of images to process")
    parser.add_argument("--min_resolution", type=int, default=512, help="Minimum image resolution")
    parser.add_argument("--max_resolution", type=int, default=2048, help="Maximum image resolution")
    parser.add_argument("--quality_threshold", type=float, default=0.7, help="Quality threshold (0-1)")
    parser.add_argument("--batch_size", type=int, default=4, help="Batch size for captioning")
    parser.add_argument("--num_workers", type=int, default=4, help="Number of worker threads")
    parser.add_argument("--no_enhance", action="store_true", help="Disable caption enhancement")
    parser.add_argument("--no_copy", action="store_true", help="Don't copy images, use references")
    parser.add_argument("--cache_dir", type=str, help="Cache directory for models")
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Check available models
    available_models = get_available_models()
    if not available_models:
        print("❌ No captioning models available!")
        print("Please install required dependencies:")
        print("pip install transformers torch torchvision")
        print("pip install clip-by-openai  # Optional for CLIP")
        return
        
    if args.captioning_model not in available_models:
        print(f"❌ Model {args.captioning_model} not available")
        print(f"Available models: {available_models}")
        return
        
    # Create preparator
    preparator = AutoDatasetPreparator(
        input_dir=args.input_dir,
        output_dir=args.output_dir,
        captioning_model=args.captioning_model,
        max_images=args.max_images,
        min_resolution=args.min_resolution,
        max_resolution=args.max_resolution,
        quality_threshold=args.quality_threshold,
        batch_size=args.batch_size,
        num_workers=args.num_workers,
        enhance_captions=not args.no_enhance,
        copy_images=not args.no_copy,
        cache_dir=args.cache_dir
    )
    
    # Prepare dataset
    stats = preparator.prepare_dataset()
    
    return stats


if __name__ == "__main__":
    main()
