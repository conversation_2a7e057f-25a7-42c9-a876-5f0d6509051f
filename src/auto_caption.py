"""
Automatic image captioning for FLUX dataset preparation
Supports multiple captioning models and techniques
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import time

import torch
from PIL import Image
import numpy as np
from tqdm.auto import tqdm

# Import captioning models
try:
    from transformers import BlipProcessor, BlipForConditionalGeneration
    BLIP_AVAILABLE = True
except ImportError:
    BLIP_AVAILABLE = False

try:
    from transformers import Blip2Processor, Blip2ForConditionalGeneration
    BLIP2_AVAILABLE = True
except ImportError:
    BLIP2_AVAILABLE = False

try:
    import clip
    CLIP_AVAILABLE = True
except ImportError:
    CLIP_AVAILABLE = False

try:
    from lavis.models import load_model_and_preprocess
    LAVIS_AVAILABLE = True
except ImportError:
    LAVIS_AVAILABLE = False


class AutoCaptioner:
    """
    Automatic image captioning system with multiple model support
    """
    
    def __init__(
        self,
        model_name: str = "blip2",
        device: str = "auto",
        batch_size: int = 4,
        max_length: int = 77,
        temperature: float = 1.0,
        cache_dir: Optional[str] = None
    ):
        self.model_name = model_name.lower()
        self.device = self._setup_device(device)
        self.batch_size = batch_size
        self.max_length = max_length
        self.temperature = temperature
        self.cache_dir = cache_dir
        
        self.logger = logging.getLogger(__name__)
        
        # Load the specified model
        self.model, self.processor = self._load_model()
        
    def _setup_device(self, device: str) -> str:
        """Setup compute device"""
        if device == "auto":
            if torch.cuda.is_available():
                return "cuda"
            elif torch.backends.mps.is_available():
                return "mps"
            else:
                return "cpu"
        return device
        
    def _load_model(self) -> Tuple[Any, Any]:
        """Load the captioning model"""
        self.logger.info(f"Loading {self.model_name} model on {self.device}")
        
        if self.model_name == "blip" and BLIP_AVAILABLE:
            return self._load_blip()
        elif self.model_name == "blip2" and BLIP2_AVAILABLE:
            return self._load_blip2()
        elif self.model_name == "clip" and CLIP_AVAILABLE:
            return self._load_clip()
        elif self.model_name == "instructblip" and LAVIS_AVAILABLE:
            return self._load_instructblip()
        else:
            # Fallback to BLIP2 if available
            if BLIP2_AVAILABLE:
                self.logger.warning(f"Model {self.model_name} not available, falling back to BLIP2")
                self.model_name = "blip2"
                return self._load_blip2()
            elif BLIP_AVAILABLE:
                self.logger.warning(f"Model {self.model_name} not available, falling back to BLIP")
                self.model_name = "blip"
                return self._load_blip()
            else:
                raise ValueError(f"No captioning models available. Please install transformers and/or clip-by-openai")
                
    def _load_blip(self) -> Tuple[Any, Any]:
        """Load BLIP model"""
        processor = BlipProcessor.from_pretrained("Salesforce/blip-image-captioning-base", cache_dir=self.cache_dir)
        model = BlipForConditionalGeneration.from_pretrained("Salesforce/blip-image-captioning-base", cache_dir=self.cache_dir)
        model.to(self.device)
        return model, processor
        
    def _load_blip2(self) -> Tuple[Any, Any]:
        """Load BLIP2 model"""
        processor = Blip2Processor.from_pretrained("Salesforce/blip2-opt-2.7b", cache_dir=self.cache_dir)
        model = Blip2ForConditionalGeneration.from_pretrained("Salesforce/blip2-opt-2.7b", cache_dir=self.cache_dir)
        model.to(self.device)
        return model, processor
        
    def _load_clip(self) -> Tuple[Any, Any]:
        """Load CLIP model for image understanding"""
        model, preprocess = clip.load("ViT-B/32", device=self.device)
        return model, preprocess
        
    def _load_instructblip(self) -> Tuple[Any, Any]:
        """Load InstructBLIP model"""
        model, vis_processors, _ = load_model_and_preprocess(
            name="blip2_t5_instruct", 
            model_type="flant5xl", 
            is_eval=True, 
            device=self.device
        )
        return model, vis_processors["eval"]
        
    def caption_image(self, image: Image.Image, prompt: Optional[str] = None) -> str:
        """
        Generate caption for a single image
        
        Args:
            image: PIL Image
            prompt: Optional prompt for guided captioning
            
        Returns:
            Generated caption
        """
        if self.model_name in ["blip", "blip2"]:
            return self._caption_with_blip(image, prompt)
        elif self.model_name == "clip":
            return self._caption_with_clip(image)
        elif self.model_name == "instructblip":
            return self._caption_with_instructblip(image, prompt)
        else:
            raise ValueError(f"Unknown model: {self.model_name}")
            
    def _caption_with_blip(self, image: Image.Image, prompt: Optional[str] = None) -> str:
        """Caption with BLIP/BLIP2"""
        try:
            if prompt:
                # Conditional captioning
                inputs = self.processor(image, prompt, return_tensors="pt").to(self.device)
            else:
                # Unconditional captioning
                inputs = self.processor(image, return_tensors="pt").to(self.device)
                
            with torch.no_grad():
                out = self.model.generate(
                    **inputs,
                    max_length=self.max_length,
                    temperature=self.temperature,
                    do_sample=True,
                    top_p=0.9,
                    num_return_sequences=1
                )
                
            caption = self.processor.decode(out[0], skip_special_tokens=True)
            
            # Clean up caption
            if prompt and caption.startswith(prompt):
                caption = caption[len(prompt):].strip()
                
            return caption.strip()
            
        except Exception as e:
            self.logger.error(f"Error captioning with {self.model_name}: {e}")
            return "An image"
            
    def _caption_with_clip(self, image: Image.Image) -> str:
        """Caption with CLIP (using predefined templates)"""
        try:
            # Predefined caption templates for CLIP
            templates = [
                "a photo of {}",
                "a picture of {}",
                "an image of {}",
                "a painting of {}",
                "a drawing of {}",
                "artwork of {}",
                "a scene with {}",
                "a view of {}"
            ]
            
            # Common objects/concepts to test
            concepts = [
                "a person", "people", "a man", "a woman", "a child",
                "a dog", "a cat", "an animal", "animals",
                "a car", "a building", "a house", "architecture",
                "nature", "a landscape", "trees", "flowers",
                "food", "a meal", "objects", "furniture",
                "art", "a painting", "abstract art",
                "the sky", "clouds", "water", "the ocean",
                "a city", "urban scene", "street scene"
            ]
            
            image_input = self.processor(image).unsqueeze(0).to(self.device)
            
            best_score = -1
            best_caption = "an image"
            
            with torch.no_grad():
                image_features = self.model.encode_image(image_input)
                
                for concept in concepts:
                    for template in templates[:3]:  # Use first 3 templates
                        text = template.format(concept)
                        text_input = clip.tokenize([text]).to(self.device)
                        text_features = self.model.encode_text(text_input)
                        
                        # Calculate similarity
                        similarity = torch.cosine_similarity(image_features, text_features).item()
                        
                        if similarity > best_score:
                            best_score = similarity
                            best_caption = text
                            
            return best_caption
            
        except Exception as e:
            self.logger.error(f"Error captioning with CLIP: {e}")
            return "an image"
            
    def _caption_with_instructblip(self, image: Image.Image, prompt: Optional[str] = None) -> str:
        """Caption with InstructBLIP"""
        try:
            image_input = self.processor(image).unsqueeze(0).to(self.device)
            
            if not prompt:
                prompt = "Describe this image in detail."
                
            with torch.no_grad():
                caption = self.model.generate({
                    "image": image_input, 
                    "prompt": prompt
                })[0]
                
            return caption.strip()
            
        except Exception as e:
            self.logger.error(f"Error captioning with InstructBLIP: {e}")
            return "An image"
            
    def caption_batch(self, images: List[Image.Image], prompts: Optional[List[str]] = None) -> List[str]:
        """
        Caption a batch of images
        
        Args:
            images: List of PIL Images
            prompts: Optional list of prompts for guided captioning
            
        Returns:
            List of generated captions
        """
        if prompts is None:
            prompts = [None] * len(images)
            
        captions = []
        
        # Process in batches
        for i in range(0, len(images), self.batch_size):
            batch_images = images[i:i + self.batch_size]
            batch_prompts = prompts[i:i + self.batch_size]
            
            batch_captions = []
            for image, prompt in zip(batch_images, batch_prompts):
                caption = self.caption_image(image, prompt)
                batch_captions.append(caption)
                
            captions.extend(batch_captions)
            
        return captions
        
    def enhance_caption(self, caption: str, image: Image.Image) -> str:
        """
        Enhance caption with additional details
        
        Args:
            caption: Original caption
            image: PIL Image for analysis
            
        Returns:
            Enhanced caption
        """
        try:
            # Analyze image properties
            width, height = image.size
            aspect_ratio = width / height
            
            # Add style descriptors based on image analysis
            enhancements = []
            
            # Aspect ratio descriptions
            if aspect_ratio > 1.5:
                enhancements.append("wide angle")
            elif aspect_ratio < 0.7:
                enhancements.append("portrait orientation")
            else:
                enhancements.append("balanced composition")
                
            # Color analysis (simplified)
            img_array = np.array(image.convert('RGB'))
            avg_brightness = np.mean(img_array)
            
            if avg_brightness > 200:
                enhancements.append("bright lighting")
            elif avg_brightness < 80:
                enhancements.append("dark atmosphere")
            else:
                enhancements.append("balanced lighting")
                
            # Combine original caption with enhancements
            if enhancements:
                enhanced = f"{caption}, {', '.join(enhancements)}"
            else:
                enhanced = caption
                
            return enhanced
            
        except Exception as e:
            self.logger.error(f"Error enhancing caption: {e}")
            return caption


def get_available_models() -> List[str]:
    """Get list of available captioning models"""
    models = []
    
    if BLIP_AVAILABLE:
        models.append("blip")
    if BLIP2_AVAILABLE:
        models.append("blip2")
    if CLIP_AVAILABLE:
        models.append("clip")
    if LAVIS_AVAILABLE:
        models.append("instructblip")
        
    return models


def install_captioning_dependencies():
    """Install required dependencies for captioning"""
    import subprocess
    import sys
    
    print("Installing captioning dependencies...")
    
    # Core dependencies
    packages = [
        "transformers>=4.40.0",
        "torch>=2.0.0",
        "torchvision>=0.15.0",
        "Pillow>=10.0.0",
        "numpy>=1.24.0",
    ]
    
    # Optional dependencies
    optional_packages = [
        "clip-by-openai",  # For CLIP
        "salesforce-lavis",  # For InstructBLIP
    ]
    
    for package in packages:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ Installed {package}")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package}")
            
    for package in optional_packages:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ Installed {package}")
        except subprocess.CalledProcessError:
            print(f"⚠️  Optional package {package} not installed")


if __name__ == "__main__":
    # Test captioning
    print("Available captioning models:", get_available_models())
    
    if not get_available_models():
        print("No captioning models available. Installing dependencies...")
        install_captioning_dependencies()
    else:
        # Test with a sample image
        try:
            captioner = AutoCaptioner(model_name="blip2")
            
            # Create a test image
            test_image = Image.new('RGB', (512, 512), color='blue')
            caption = captioner.caption_image(test_image)
            print(f"Test caption: {caption}")
            
        except Exception as e:
            print(f"Error testing captioner: {e}")
