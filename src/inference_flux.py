"""
Inference script for finetuned FLUX.1-dev text-to-image model
"""

import os
import argparse
from pathlib import Path
from typing import Optional, List

import torch
import numpy as np
from PIL import Image
from diffusers import FluxPipeline
from peft import PeftModel

def load_finetuned_flux_pipeline(
    base_model_path: str,
    lora_path: Optional[str] = None,
    device: str = "cuda",
    torch_dtype = torch.bfloat16,
):
    """Load finetuned FLUX pipeline"""
    
    # Load base pipeline
    pipe = FluxPipeline.from_pretrained(
        base_model_path,
        torch_dtype=torch_dtype,
        device_map="auto" if device == "auto" else None,
    )
    
    # Load LoRA weights if provided
    if lora_path and os.path.exists(lora_path):
        print(f"Loading LoRA weights from {lora_path}")
        pipe.load_lora_weights(lora_path)
    
    if device != "auto":
        pipe.to(device)
    
    return pipe


def generate_image(
    pipeline: FluxPipeline,
    prompt: str,
    negative_prompt: str = "",
    output_path: str = "output.png",
    width: int = 1024,
    height: int = 1024,
    guidance_scale: float = 3.5,
    num_inference_steps: int = 28,
    seed: Optional[int] = None,
    num_images: int = 1,
):
    """Generate image from text prompt"""
    
    # Setup generator for reproducibility
    generator = None
    if seed is not None:
        generator = torch.Generator(device=pipeline.device).manual_seed(seed)
    
    print(f"Generating image...")
    print(f"Prompt: {prompt}")
    print(f"Negative prompt: {negative_prompt}")
    print(f"Size: {width}x{height}")
    print(f"Steps: {num_inference_steps}, Guidance: {guidance_scale}")
    
    # Generate image
    with torch.no_grad():
        result = pipeline(
            prompt=prompt,
            negative_prompt=negative_prompt if negative_prompt else None,
            height=height,
            width=width,
            guidance_scale=guidance_scale,
            num_inference_steps=num_inference_steps,
            generator=generator,
            num_images_per_prompt=num_images,
        )
    
    # Save images
    if num_images == 1:
        result.images[0].save(output_path)
        print(f"Image saved to: {output_path}")
        return output_path
    else:
        output_paths = []
        base_path = Path(output_path)
        for i, image in enumerate(result.images):
            if i == 0:
                save_path = output_path
            else:
                save_path = base_path.parent / f"{base_path.stem}_{i+1}{base_path.suffix}"
            image.save(save_path)
            output_paths.append(str(save_path))
            print(f"Image {i+1} saved to: {save_path}")
        return output_paths


def batch_generate(
    pipeline: FluxPipeline,
    prompts: List[str],
    output_dir: str = "outputs",
    **kwargs
):
    """Generate multiple images from a list of prompts"""
    
    os.makedirs(output_dir, exist_ok=True)
    
    results = []
    for i, prompt in enumerate(prompts):
        output_path = os.path.join(output_dir, f"image_{i+1:03d}.png")
        result_path = generate_image(
            pipeline=pipeline,
            prompt=prompt,
            output_path=output_path,
            **kwargs
        )
        results.append(result_path)
        
    print(f"\nBatch generation complete! {len(results)} images saved to {output_dir}")
    return results


def main():
    parser = argparse.ArgumentParser(description="Generate images with finetuned FLUX.1-dev model")
    parser.add_argument(
        "--base_model_path",
        type=str,
        default="black-forest-labs/FLUX.1-dev",
        help="Path to base FLUX model"
    )
    parser.add_argument(
        "--lora_path",
        type=str,
        default=None,
        help="Path to LoRA weights directory"
    )
    parser.add_argument(
        "--prompt",
        type=str,
        required=True,
        help="Text prompt for image generation"
    )
    parser.add_argument(
        "--negative_prompt",
        type=str,
        default="blurry, low quality, distorted, ugly, deformed",
        help="Negative prompt"
    )
    parser.add_argument(
        "--output_path",
        type=str,
        default="generated_image.png",
        help="Output image path"
    )
    parser.add_argument(
        "--width",
        type=int,
        default=1024,
        help="Image width"
    )
    parser.add_argument(
        "--height",
        type=int,
        default=1024,
        help="Image height"
    )
    parser.add_argument(
        "--guidance_scale",
        type=float,
        default=3.5,
        help="Guidance scale for generation"
    )
    parser.add_argument(
        "--num_inference_steps",
        type=int,
        default=28,
        help="Number of inference steps"
    )
    parser.add_argument(
        "--seed",
        type=int,
        default=None,
        help="Random seed for reproducibility"
    )
    parser.add_argument(
        "--num_images",
        type=int,
        default=1,
        help="Number of images to generate"
    )
    parser.add_argument(
        "--device",
        type=str,
        default="cuda",
        help="Device to use for inference"
    )
    parser.add_argument(
        "--batch_prompts",
        type=str,
        default=None,
        help="Path to text file with prompts (one per line) for batch generation"
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default="outputs",
        help="Output directory for batch generation"
    )
    
    args = parser.parse_args()
    
    # Load pipeline
    print("Loading FLUX pipeline...")
    pipeline = load_finetuned_flux_pipeline(
        base_model_path=args.base_model_path,
        lora_path=args.lora_path,
        device=args.device,
    )
    
    # Batch generation if prompts file provided
    if args.batch_prompts:
        if not os.path.exists(args.batch_prompts):
            raise FileNotFoundError(f"Prompts file not found: {args.batch_prompts}")
            
        with open(args.batch_prompts, 'r') as f:
            prompts = [line.strip() for line in f if line.strip()]
            
        print(f"Loaded {len(prompts)} prompts for batch generation")
        
        batch_generate(
            pipeline=pipeline,
            prompts=prompts,
            output_dir=args.output_dir,
            negative_prompt=args.negative_prompt,
            width=args.width,
            height=args.height,
            guidance_scale=args.guidance_scale,
            num_inference_steps=args.num_inference_steps,
            seed=args.seed,
        )
    else:
        # Single image generation
        output_path = generate_image(
            pipeline=pipeline,
            prompt=args.prompt,
            negative_prompt=args.negative_prompt,
            output_path=args.output_path,
            width=args.width,
            height=args.height,
            guidance_scale=args.guidance_scale,
            num_inference_steps=args.num_inference_steps,
            seed=args.seed,
            num_images=args.num_images,
        )
        
        print(f"Generation complete! Image(s) saved to: {output_path}")


if __name__ == "__main__":
    main()
