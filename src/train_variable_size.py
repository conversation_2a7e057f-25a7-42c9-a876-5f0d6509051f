"""
Enhanced training script for variable-sized images
Supports both Wan2.1 and FLUX models with aspect ratio preservation
"""

import os
import sys
import math
import logging
import argparse
from pathlib import Path
from typing import Dict, Any, Optional

import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
import numpy as np
from PIL import Image
from tqdm.auto import tqdm
import wandb

from accelerate import Accelerator
from accelerate.logging import get_logger
from accelerate.utils import ProjectConfiguration, set_seed

import transformers
from diffusers.optimization import get_scheduler

from omegaconf import OmegaConf
from dataset_variable_size import VariableSizeImageVideoDataset, VariableSizeFluxDataset
from data_collator import create_variable_size_dataloader

# Setup logging
logger = get_logger(__name__, log_level="INFO")


def parse_args():
    parser = argparse.ArgumentParser(description="Train models with variable-sized images")
    parser.add_argument(
        "--config",
        type=str,
        required=True,
        help="Path to config file"
    )
    parser.add_argument(
        "--model_type",
        type=str,
        choices=["wan2.1", "flux"],
        required=True,
        help="Model type to train"
    )
    parser.add_argument(
        "--resume_from_checkpoint",
        type=str,
        default=None,
        help="Path to checkpoint to resume from"
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default=None,
        help="Output directory (overrides config)"
    )
    parser.add_argument(
        "--logging_dir",
        type=str,
        default=None,
        help="Logging directory (overrides config)"
    )
    parser.add_argument(
        "--mixed_precision",
        type=str,
        default=None,
        choices=["no", "fp16", "bf16"],
        help="Mixed precision mode"
    )
    parser.add_argument(
        "--report_to",
        type=str,
        default=None,
        help="Reporting service (wandb, tensorboard, etc.)"
    )
    
    args = parser.parse_args()
    return args


def load_config(config_path: str) -> Dict[str, Any]:
    """Load configuration from YAML file"""
    config = OmegaConf.load(config_path)
    return OmegaConf.to_container(config, resolve=True)


def setup_logging(logging_dir: str, accelerator: Accelerator):
    """Setup logging configuration"""
    if accelerator.is_main_process:
        transformers.utils.logging.set_verbosity_warning()
        logging.basicConfig(
            format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
            datefmt="%m/%d/%Y %H:%M:%S",
            level=logging.INFO,
        )
    else:
        transformers.utils.logging.set_verbosity_error()
        logging.basicConfig(level=logging.ERROR)


def create_variable_size_dataset(config: Dict[str, Any], model_type: str):
    """Create variable-size dataset based on model type"""
    dataset_config = config["dataset"]
    training_config = config["training"]
    variable_size_config = config.get("variable_size", {})
    
    if model_type == "wan2.1":
        # Create Wan2.1 variable-size dataset
        bucket_sizes = training_config.get("bucket_sizes", None)
        
        train_dataset = VariableSizeImageVideoDataset(
            data_dir=dataset_config["train_data_dir"],
            image_column=dataset_config["image_column"],
            video_column=dataset_config["video_column"],
            caption_column=dataset_config["caption_column"],
            max_width=training_config["max_width"],
            max_height=training_config["max_height"],
            num_frames=training_config["num_frames"],
            frame_rate=training_config["frame_rate"],
            random_flip=dataset_config.get("random_flip", 0.0),
            normalize=dataset_config.get("normalize", True),
            max_sequence_length=dataset_config["max_sequence_length"],
            resize_mode=dataset_config.get("resize_mode", "pad"),
            bucket_sizes=bucket_sizes,
        )
        
    elif model_type == "flux":
        # Create FLUX variable-size dataset
        bucket_sizes = training_config.get("bucket_sizes", None)
        
        train_dataset = VariableSizeFluxDataset(
            data_dir=dataset_config["train_data_dir"],
            image_column=dataset_config["image_column"],
            caption_column=dataset_config["caption_column"],
            max_resolution=training_config["max_resolution"],
            random_flip=dataset_config.get("random_flip", 0.0),
            normalize=dataset_config.get("normalize", True),
            max_sequence_length=dataset_config["max_sequence_length"],
            resize_mode=dataset_config.get("resize_mode", "pad"),
            bucket_sizes=bucket_sizes,
        )
    else:
        raise ValueError(f"Unsupported model type: {model_type}")
    
    return train_dataset


def create_dataloader(dataset, config: Dict[str, Any]):
    """Create variable-size dataloader"""
    training_config = config["training"]
    
    dataloader = create_variable_size_dataloader(
        dataset=dataset,
        batch_size=training_config["train_batch_size"],
        shuffle=True,
        num_workers=training_config["dataloader_num_workers"],
        pin_memory=training_config.get("pin_memory", True),
        drop_last=False,
        use_bucket_sampler=training_config.get("use_bucket_sampler", True),
        use_aspect_ratio_grouping=training_config.get("use_aspect_ratio_grouping", False),
    )
    
    return dataloader


def load_models_wan2_1(config: Dict[str, Any], accelerator: Accelerator):
    """Load Wan2.1 models with LoRA support"""
    # Import here to avoid circular imports
    from train_wan2_1 import load_models
    return load_models(config, accelerator)


def load_models_flux(config: Dict[str, Any], accelerator: Accelerator):
    """Load FLUX models with LoRA support"""
    # Import here to avoid circular imports
    from train_flux import load_models
    return load_models(config, accelerator)


def compute_loss_wan2_1(models, batch, config, accelerator):
    """Compute loss for Wan2.1 with variable sizes"""
    # Import here to avoid circular imports
    from train_wan2_1 import compute_loss
    return compute_loss(models, batch, config, accelerator)


def compute_loss_flux(models, batch, config, accelerator):
    """Compute loss for FLUX with variable sizes"""
    # Import here to avoid circular imports
    from train_flux import compute_loss
    return compute_loss(models, batch, config, accelerator)


def validate_model_wan2_1(models, config, accelerator, step):
    """Validate Wan2.1 model"""
    # Import here to avoid circular imports
    from train_wan2_1 import validate_model
    return validate_model(models, config, accelerator, step)


def validate_model_flux(models, config, accelerator, step):
    """Validate FLUX model"""
    # Import here to avoid circular imports
    from train_flux import validate_model
    return validate_model(models, config, accelerator, step)


def log_variable_size_stats(batch, step, accelerator):
    """Log statistics about variable sizes in the batch"""
    if not accelerator.is_main_process:
        return
        
    if "bucket_id" in batch:
        bucket_ids = batch["bucket_id"]
        unique_buckets = set(bucket_ids)
        
        logger.info(f"Step {step}: Batch contains {len(unique_buckets)} unique bucket(s): {unique_buckets}")
        
        # Log to wandb if available
        try:
            import wandb
            if wandb.run is not None:
                wandb.log({
                    "batch_unique_buckets": len(unique_buckets),
                    "batch_bucket_ids": list(unique_buckets),
                }, step=step)
        except:
            pass


def main():
    args = parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Override config with command line arguments
    if args.output_dir:
        config["training"]["output_dir"] = args.output_dir
    if args.logging_dir:
        config["training"]["logging_dir"] = args.logging_dir
    if args.mixed_precision:
        config["training"]["mixed_precision"] = args.mixed_precision
    if args.report_to:
        config["training"]["report_to"] = args.report_to
    if args.resume_from_checkpoint:
        config["training"]["resume_from_checkpoint"] = args.resume_from_checkpoint
    
    # Setup accelerator
    accelerator_project_config = ProjectConfiguration(
        project_dir=config["training"]["output_dir"],
        logging_dir=config["training"]["logging_dir"],
    )
    
    accelerator = Accelerator(
        gradient_accumulation_steps=config["training"]["gradient_accumulation_steps"],
        mixed_precision=config["training"]["mixed_precision"],
        log_with=config["training"]["report_to"],
        project_config=accelerator_project_config,
    )
    
    # Setup logging
    setup_logging(config["training"]["logging_dir"], accelerator)
    
    # Set seed
    if config["training"]["seed"] is not None:
        set_seed(config["training"]["seed"])
    
    # Create output directory
    os.makedirs(config["training"]["output_dir"], exist_ok=True)
    
    # Initialize wandb
    if accelerator.is_main_process and config["training"]["report_to"] == "wandb":
        wandb.init(
            project=config["wandb"]["project_name"],
            name=config["wandb"]["run_name"],
            tags=config["wandb"]["tags"],
            notes=config["wandb"]["notes"],
            config=config,
        )
    
    # Load models based on type
    logger.info(f"Loading {args.model_type} models...")
    if args.model_type == "wan2.1":
        models = load_models_wan2_1(config, accelerator)
        compute_loss_fn = compute_loss_wan2_1
        validate_fn = validate_model_wan2_1
    elif args.model_type == "flux":
        models = load_models_flux(config, accelerator)
        compute_loss_fn = compute_loss_flux
        validate_fn = validate_model_flux
    else:
        raise ValueError(f"Unsupported model type: {args.model_type}")
    
    # Create variable-size dataset
    logger.info("Creating variable-size dataset...")
    train_dataset = create_variable_size_dataset(config, args.model_type)
    
    # Create variable-size dataloader
    logger.info("Creating variable-size dataloader...")
    train_dataloader = create_dataloader(train_dataset, config)
    
    # Setup optimizer
    if config["training"]["use_8bit_adam"]:
        import bitsandbytes as bnb
        optimizer_cls = bnb.optim.AdamW8bit
    else:
        optimizer_cls = torch.optim.AdamW
    
    # Get the main model for optimization
    main_model_key = "transformer" if args.model_type in ["wan2.1", "flux"] else "unet"
    main_model = models[main_model_key]
    
    optimizer = optimizer_cls(
        main_model.parameters(),
        lr=config["training"]["learning_rate"],
        betas=(config["training"]["adam_beta1"], config["training"]["adam_beta2"]),
        weight_decay=config["training"]["adam_weight_decay"],
        eps=config["training"]["adam_epsilon"],
    )
    
    # Setup learning rate scheduler
    lr_scheduler = get_scheduler(
        config["training"]["lr_scheduler"],
        optimizer=optimizer,
        num_warmup_steps=config["training"]["lr_warmup_steps"],
        num_training_steps=config["training"]["max_train_steps"],
        num_cycles=config["training"].get("lr_num_cycles", 1),
    )
    
    # Prepare everything with accelerator
    main_model, optimizer, train_dataloader, lr_scheduler = accelerator.prepare(
        main_model, optimizer, train_dataloader, lr_scheduler
    )
    
    # Update models dict with prepared model
    models[main_model_key] = main_model
    
    # Move other models to device
    for key, model in models.items():
        if key != main_model_key and hasattr(model, 'to'):
            model.to(accelerator.device)
    
    # Calculate total training steps
    num_update_steps_per_epoch = math.ceil(len(train_dataloader) / config["training"]["gradient_accumulation_steps"])
    max_train_steps = config["training"]["max_train_steps"]
    num_train_epochs = config["training"]["num_train_epochs"]
    
    if max_train_steps is None:
        max_train_steps = num_train_epochs * num_update_steps_per_epoch
    else:
        num_train_epochs = math.ceil(max_train_steps / num_update_steps_per_epoch)
    
    logger.info(f"***** Running variable-size training *****")
    logger.info(f"  Model type = {args.model_type}")
    logger.info(f"  Num examples = {len(train_dataset)}")
    logger.info(f"  Num epochs = {num_train_epochs}")
    logger.info(f"  Instantaneous batch size per device = {config['training']['train_batch_size']}")
    logger.info(f"  Total train batch size (w. parallel, distributed & accumulation) = {config['training']['train_batch_size'] * accelerator.num_processes * config['training']['gradient_accumulation_steps']}")
    logger.info(f"  Gradient Accumulation steps = {config['training']['gradient_accumulation_steps']}")
    logger.info(f"  Total optimization steps = {max_train_steps}")
    logger.info(f"  Variable size buckets = {len(train_dataset.bucket_sizes) if hasattr(train_dataset, 'bucket_sizes') else 'N/A'}")
    
    # Training loop
    global_step = 0
    first_epoch = 0
    
    # Resume from checkpoint if specified
    if config["training"]["resume_from_checkpoint"]:
        accelerator.load_state(config["training"]["resume_from_checkpoint"])
        global_step = int(config["training"]["resume_from_checkpoint"].split("-")[-1])
        first_epoch = global_step // num_update_steps_per_epoch
        
    progress_bar = tqdm(
        range(0, max_train_steps),
        initial=global_step,
        desc="Steps",
        disable=not accelerator.is_local_main_process,
    )
    
    for epoch in range(first_epoch, num_train_epochs):
        main_model.train()
        
        for step, batch in enumerate(train_dataloader):
            with accelerator.accumulate(main_model):
                # Log variable size statistics
                if global_step % (config["training"]["logging_steps"] * 5) == 0:
                    log_variable_size_stats(batch, global_step, accelerator)
                
                # Compute loss
                loss = compute_loss_fn(models, batch, config, accelerator)
                
                # Backward pass
                accelerator.backward(loss)
                
                if accelerator.sync_gradients:
                    accelerator.clip_grad_norm_(main_model.parameters(), config["training"]["max_grad_norm"])
                
                optimizer.step()
                lr_scheduler.step()
                optimizer.zero_grad()
            
            # Update progress
            if accelerator.sync_gradients:
                progress_bar.update(1)
                global_step += 1
                
                # Logging
                if global_step % config["training"]["logging_steps"] == 0:
                    logs = {
                        "loss": loss.detach().item(),
                        "lr": lr_scheduler.get_last_lr()[0],
                        "step": global_step,
                        "epoch": epoch,
                    }
                    
                    if config["training"]["report_to"] == "wandb" and accelerator.is_main_process:
                        wandb.log(logs, step=global_step)
                    
                    logger.info(f"Step {global_step}: loss={loss.detach().item():.4f}, lr={lr_scheduler.get_last_lr()[0]:.2e}")
                
                # Validation
                if global_step % config["training"]["validation_steps"] == 0:
                    validate_fn(models, config, accelerator, global_step)
                
                # Save checkpoint
                if global_step % config["training"]["checkpointing_steps"] == 0:
                    if accelerator.is_main_process:
                        save_path = os.path.join(config["training"]["output_dir"], f"checkpoint-{global_step}")
                        accelerator.save_state(save_path)
                        logger.info(f"Saved checkpoint: {save_path}")
                
                # Check if we've reached max steps
                if global_step >= max_train_steps:
                    break
        
        if global_step >= max_train_steps:
            break
    
    # Save final model
    accelerator.wait_for_everyone()
    if accelerator.is_main_process:
        final_model = accelerator.unwrap_model(main_model)
        
        if config["model"].get("use_lora", False):
            # Save LoRA weights
            final_model.save_pretrained(os.path.join(config["training"]["output_dir"], "lora"))
        else:
            # Save full model
            final_model.save_pretrained(os.path.join(config["training"]["output_dir"], main_model_key))
        
        logger.info(f"Variable-size training completed. Model saved to {config['training']['output_dir']}")
    
    accelerator.end_training()


if __name__ == "__main__":
    main()
