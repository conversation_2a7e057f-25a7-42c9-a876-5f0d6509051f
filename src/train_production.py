#!/usr/bin/env python3
"""
Production-ready training script with environment management
Supports both Wan2.1 and FLUX models with variable-size training
"""

import os
import sys
import math
import argparse
from pathlib import Path
from typing import Dict, Any

import torch
from tqdm.auto import tqdm

from accelerate import Accelerator
from accelerate.utils import ProjectConfiguration, set_seed
import transformers
from diffusers.optimization import get_scheduler

# Production imports
from config_manager import get_config_manager, load_config
from auth_manager import create_auth_manager
from production_utils import ProductionLogger, setup_production_environment


def parse_args():
    parser = argparse.ArgumentParser(description="Production training for diffusion models")
    parser.add_argument("--config", type=str, required=True, help="Path to config file")
    parser.add_argument("--model_type", type=str, choices=["wan2.1", "flux"], required=True, help="Model type")
    parser.add_argument("--resume_from_checkpoint", type=str, default=None, help="Checkpoint to resume from")
    parser.add_argument("--output_dir", type=str, default=None, help="Output directory override")
    parser.add_argument("--logging_dir", type=str, default=None, help="Logging directory override")
    parser.add_argument("--mixed_precision", type=str, default=None, choices=["no", "fp16", "bf16"], help="Mixed precision")
    parser.add_argument("--report_to", type=str, default=None, help="Reporting service")
    parser.add_argument("--variable_size", action="store_true", help="Enable variable-size training")
    
    return parser.parse_args()


def setup_production_training():
    """Setup production environment and return components"""
    # Initialize configuration manager
    config_manager = get_config_manager()
    config_manager.setup_all()
    
    # Setup authentication
    auth_manager = create_auth_manager(config_manager)
    
    # Setup production utilities
    prod_env = setup_production_environment(config_manager)
    
    # Create production logger
    logger = ProductionLogger("ProductionTraining")
    
    return config_manager, auth_manager, prod_env, logger


def load_models_and_datasets(config: Dict[str, Any], model_type: str, variable_size: bool, logger: ProductionLogger):
    """Load models and datasets based on configuration"""
    logger.info(f"Loading {model_type} models and datasets", variable_size=variable_size)
    
    if variable_size:
        # Use variable-size datasets
        if model_type == "wan2.1":
            from dataset_variable_size import VariableSizeImageVideoDataset
            from data_collator import create_variable_size_dataloader
            
            dataset = VariableSizeImageVideoDataset(
                data_dir=config["dataset"]["train_data_dir"],
                image_column=config["dataset"]["image_column"],
                video_column=config["dataset"]["video_column"],
                caption_column=config["dataset"]["caption_column"],
                max_width=config["training"]["max_width"],
                max_height=config["training"]["max_height"],
                num_frames=config["training"]["num_frames"],
                resize_mode=config["dataset"].get("resize_mode", "pad"),
            )
            
            dataloader = create_variable_size_dataloader(
                dataset=dataset,
                batch_size=config["training"]["train_batch_size"],
                shuffle=True,
                num_workers=config["training"]["dataloader_num_workers"],
                use_bucket_sampler=True,
            )
            
        elif model_type == "flux":
            from dataset_variable_size import VariableSizeFluxDataset
            from data_collator import create_variable_size_dataloader
            
            dataset = VariableSizeFluxDataset(
                data_dir=config["dataset"]["train_data_dir"],
                image_column=config["dataset"]["image_column"],
                caption_column=config["dataset"]["caption_column"],
                max_resolution=config["training"]["max_resolution"],
                resize_mode=config["dataset"].get("resize_mode", "pad"),
            )
            
            dataloader = create_variable_size_dataloader(
                dataset=dataset,
                batch_size=config["training"]["train_batch_size"],
                shuffle=True,
                num_workers=config["training"]["dataloader_num_workers"],
                use_bucket_sampler=True,
            )
    else:
        # Use standard datasets
        if model_type == "wan2.1":
            from dataset import ImageVideoDataset
            from torch.utils.data import DataLoader
            
            dataset = ImageVideoDataset(
                data_dir=config["dataset"]["train_data_dir"],
                image_column=config["dataset"]["image_column"],
                video_column=config["dataset"]["video_column"],
                caption_column=config["dataset"]["caption_column"],
                width=config["training"]["width"],
                height=config["training"]["height"],
                num_frames=config["training"]["num_frames"],
            )
            
            dataloader = DataLoader(
                dataset,
                batch_size=config["training"]["train_batch_size"],
                shuffle=True,
                num_workers=config["training"]["dataloader_num_workers"],
                pin_memory=config["training"].get("pin_memory", True),
            )
            
        elif model_type == "flux":
            from dataset_flux import FluxTextToImageDataset
            from torch.utils.data import DataLoader
            
            dataset = FluxTextToImageDataset(
                data_dir=config["dataset"]["train_data_dir"],
                image_column=config["dataset"]["image_column"],
                caption_column=config["dataset"]["caption_column"],
                resolution=config["training"]["resolution"],
            )
            
            dataloader = DataLoader(
                dataset,
                batch_size=config["training"]["train_batch_size"],
                shuffle=True,
                num_workers=config["training"]["dataloader_num_workers"],
                pin_memory=config["training"].get("pin_memory", True),
            )
    
    # Load models
    if model_type == "wan2.1":
        from train_wan2_1 import load_models
        models = load_models(config, None)  # Will be prepared with accelerator later
    elif model_type == "flux":
        from train_flux import load_models
        models = load_models(config, None)  # Will be prepared with accelerator later
    
    logger.info(f"Successfully loaded {model_type} models and dataset", dataset_size=len(dataset))
    
    return models, dataset, dataloader


def setup_training_components(models: Dict[str, Any], config: Dict[str, Any], model_type: str, logger: ProductionLogger):
    """Setup optimizer, scheduler, and other training components"""
    
    # Get the main model for training
    main_model_key = "transformer" if model_type in ["wan2.1", "flux"] else "unet"
    main_model = models[main_model_key]
    
    # Setup optimizer
    if config["training"]["use_8bit_adam"]:
        try:
            import bitsandbytes as bnb
            optimizer_cls = bnb.optim.AdamW8bit
            logger.info("Using 8-bit Adam optimizer")
        except ImportError:
            logger.warning("bitsandbytes not available, falling back to standard Adam")
            optimizer_cls = torch.optim.AdamW
    else:
        optimizer_cls = torch.optim.AdamW
    
    optimizer = optimizer_cls(
        main_model.parameters(),
        lr=config["training"]["learning_rate"],
        betas=(config["training"]["adam_beta1"], config["training"]["adam_beta2"]),
        weight_decay=config["training"]["adam_weight_decay"],
        eps=config["training"]["adam_epsilon"],
    )
    
    # Setup learning rate scheduler
    lr_scheduler = get_scheduler(
        config["training"]["lr_scheduler"],
        optimizer=optimizer,
        num_warmup_steps=config["training"]["lr_warmup_steps"],
        num_training_steps=config["training"]["max_train_steps"],
        num_cycles=config["training"].get("lr_num_cycles", 1),
    )
    
    logger.info("Training components setup complete")
    
    return main_model, optimizer, lr_scheduler, main_model_key


def run_training_loop(
    models: Dict[str, Any],
    main_model: torch.nn.Module,
    optimizer: torch.optim.Optimizer,
    lr_scheduler: Any,
    dataloader: Any,
    config: Dict[str, Any],
    model_type: str,
    accelerator: Accelerator,
    logger: ProductionLogger,
    prod_env: Dict[str, Any]
):
    """Run the main training loop"""
    
    # Get compute loss function
    if model_type == "wan2.1":
        from train_wan2_1 import compute_loss
    elif model_type == "flux":
        from train_flux import compute_loss
    
    # Setup monitoring
    training_monitor = prod_env.get("training_monitor")
    error_handler = prod_env.get("error_handler")
    notification_manager = prod_env.get("notification_manager")
    
    if training_monitor:
        training_monitor.start_training()
    
    if notification_manager:
        notification_manager.notify_training_start(config)
    
    # Training parameters
    max_train_steps = config["training"]["max_train_steps"]
    gradient_accumulation_steps = config["training"]["gradient_accumulation_steps"]
    logging_steps = config["training"]["logging_steps"]
    validation_steps = config["training"]["validation_steps"]
    checkpointing_steps = config["training"]["checkpointing_steps"]
    
    # Training loop
    global_step = 0
    
    # Setup W&B if configured
    if config["training"]["report_to"] == "wandb" and accelerator.is_main_process:
        try:
            import wandb
            wandb.init(
                project=config["wandb"]["project_name"],
                name=config["wandb"]["run_name"],
                tags=config["wandb"]["tags"],
                notes=config["wandb"]["notes"],
                config=config,
            )
            logger.info("Weights & Biases initialized")
        except ImportError:
            logger.warning("wandb not available, skipping W&B logging")
    
    logger.info(f"Starting training loop for {max_train_steps} steps")
    
    progress_bar = tqdm(
        range(max_train_steps),
        desc="Training",
        disable=not accelerator.is_local_main_process,
    )
    
    try:
        for epoch in range(config["training"]["num_train_epochs"]):
            main_model.train()
            
            for step, batch in enumerate(dataloader):
                with accelerator.accumulate(main_model):
                    # Compute loss
                    loss = compute_loss(models, batch, config, accelerator)
                    
                    # Backward pass
                    accelerator.backward(loss)
                    
                    if accelerator.sync_gradients:
                        accelerator.clip_grad_norm_(main_model.parameters(), config["training"]["max_grad_norm"])
                    
                    optimizer.step()
                    lr_scheduler.step()
                    optimizer.zero_grad()
                
                # Update progress
                if accelerator.sync_gradients:
                    progress_bar.update(1)
                    global_step += 1
                    
                    # Logging
                    if global_step % logging_steps == 0:
                        logs = {
                            "loss": loss.detach().item(),
                            "lr": lr_scheduler.get_last_lr()[0],
                            "step": global_step,
                            "epoch": epoch,
                        }
                        
                        if training_monitor:
                            training_monitor.log_step(global_step, loss.detach().item(), lr_scheduler.get_last_lr()[0])
                        
                        # Log to W&B if available
                        try:
                            import wandb
                            if wandb.run is not None and accelerator.is_main_process:
                                wandb.log(logs, step=global_step)
                        except:
                            pass
                        
                        logger.info(f"Step {global_step}", **logs)
                    
                    # Validation
                    if global_step % validation_steps == 0:
                        logger.info(f"Running validation at step {global_step}")
                        # Validation logic would go here
                    
                    # Save checkpoint
                    if global_step % checkpointing_steps == 0:
                        if accelerator.is_main_process:
                            save_path = os.path.join(config["training"]["output_dir"], f"checkpoint-{global_step}")
                            
                            if error_handler:
                                success = error_handler.safe_save_checkpoint(accelerator.unwrap_model(main_model), save_path)
                                if success and training_monitor:
                                    training_monitor.log_checkpoint(global_step, save_path)
                            else:
                                accelerator.save_state(save_path)
                                logger.info(f"Saved checkpoint: {save_path}")
                    
                    # Check if we've reached max steps
                    if global_step >= max_train_steps:
                        break
            
            if global_step >= max_train_steps:
                break
                
    except Exception as e:
        logger.error("Training error occurred", exception=e)
        
        if error_handler and "cuda out of memory" in str(e).lower():
            error_handler.handle_cuda_oom("training")
        
        if notification_manager:
            notification_manager.notify_error(str(e))
        
        raise
    
    # Training completed
    logger.info("Training completed successfully")
    
    # Get training summary
    if training_monitor:
        summary = training_monitor.get_training_summary()
        logger.info("Training summary", **summary)
        
        if notification_manager:
            notification_manager.notify_training_complete(summary)
    
    return global_step


def main():
    """Main training function"""
    args = parse_args()
    
    # Setup production environment
    config_manager, auth_manager, prod_env, logger = setup_production_training()
    
    logger.info("Starting production training", model_type=args.model_type, variable_size=args.variable_size)
    
    # Load configuration
    config = load_config(args.config)
    
    # Apply command line overrides
    if args.output_dir:
        config["training"]["output_dir"] = args.output_dir
    if args.logging_dir:
        config["training"]["logging_dir"] = args.logging_dir
    if args.mixed_precision:
        config["training"]["mixed_precision"] = args.mixed_precision
    if args.report_to:
        config["training"]["report_to"] = args.report_to
    if args.resume_from_checkpoint:
        config["training"]["resume_from_checkpoint"] = args.resume_from_checkpoint
    
    # Setup accelerator
    accelerator_project_config = ProjectConfiguration(
        project_dir=config["training"]["output_dir"],
        logging_dir=config["training"]["logging_dir"],
    )
    
    accelerator = Accelerator(
        gradient_accumulation_steps=config["training"]["gradient_accumulation_steps"],
        mixed_precision=config["training"]["mixed_precision"],
        log_with=config["training"]["report_to"],
        project_config=accelerator_project_config,
    )
    
    # Set seed
    if config["training"]["seed"] is not None:
        set_seed(config["training"]["seed"])
    
    # Create output directory
    os.makedirs(config["training"]["output_dir"], exist_ok=True)
    
    # Load models and datasets
    models, dataset, dataloader = load_models_and_datasets(config, args.model_type, args.variable_size, logger)
    
    # Setup training components
    main_model, optimizer, lr_scheduler, main_model_key = setup_training_components(models, config, args.model_type, logger)
    
    # Prepare with accelerator
    main_model, optimizer, dataloader, lr_scheduler = accelerator.prepare(
        main_model, optimizer, dataloader, lr_scheduler
    )
    
    # Update models dict
    models[main_model_key] = main_model
    
    # Move other models to device
    for key, model in models.items():
        if key != main_model_key and hasattr(model, 'to'):
            model.to(accelerator.device)
    
    # Run training
    final_step = run_training_loop(
        models, main_model, optimizer, lr_scheduler, dataloader,
        config, args.model_type, accelerator, logger, prod_env
    )
    
    # Save final model
    accelerator.wait_for_everyone()
    if accelerator.is_main_process:
        final_model = accelerator.unwrap_model(main_model)
        
        if config["model"].get("use_lora", False):
            final_model.save_pretrained(os.path.join(config["training"]["output_dir"], "lora"))
        else:
            final_model.save_pretrained(os.path.join(config["training"]["output_dir"], main_model_key))
        
        logger.info(f"Training completed. Model saved to {config['training']['output_dir']}")
    
    # Cleanup
    if "system_monitor" in prod_env:
        prod_env["system_monitor"].stop_monitoring()
    
    accelerator.end_training()


if __name__ == "__main__":
    main()
