#!/usr/bin/env python3
"""
Environment Setup Script for Diffusion Finetuning Toolkit
Helps create and validate .env file for RunPod A40 deployment
"""

import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional


def create_directories(base_path: str = "/workspace") -> None:
    """Create necessary directories"""
    directories = [
        f"{base_path}/cache/huggingface",
        f"{base_path}/cache/transformers", 
        f"{base_path}/cache/diffusers",
        f"{base_path}/cache/datasets",
        f"{base_path}/cache/models",
        f"{base_path}/cache/flux",
        f"{base_path}/cache/wan21",
        f"{base_path}/models",
        f"{base_path}/data",
        f"{base_path}/outputs",
        f"{base_path}/logs",
        f"{base_path}/tmp",
        f"{base_path}/backups"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")


def get_user_input(prompt: str, default: str = "", required: bool = False) -> str:
    """Get user input with validation"""
    while True:
        value = input(f"{prompt} [{default}]: ").strip()
        if not value:
            value = default
        
        if required and not value:
            print("❌ This field is required!")
            continue
        
        return value


def detect_runpod_environment() -> Dict[str, Any]:
    """Detect RunPod environment variables"""
    runpod_vars = {}
    
    # Common RunPod environment variables
    runpod_env_vars = [
        "RUNPOD_POD_ID",
        "RUNPOD_PUBLIC_IP", 
        "RUNPOD_TCP_PORT_22",
        "RUNPOD_CPU_COUNT",
        "RUNPOD_MEM_GB",
        "RUNPOD_GPU_COUNT"
    ]
    
    for var in runpod_env_vars:
        value = os.environ.get(var)
        if value:
            runpod_vars[var] = value
            print(f"🔍 Detected RunPod variable: {var}={value}")
    
    return runpod_vars


def validate_gpu() -> bool:
    """Validate GPU availability"""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"✅ GPU detected: {gpu_name} ({gpu_memory:.1f}GB)")
            
            if "A40" in gpu_name:
                print("🎯 A40 GPU detected - optimal for diffusion training!")
                return True
            else:
                print(f"⚠️  Non-A40 GPU detected: {gpu_name}")
                return True
        else:
            print("❌ No GPU detected!")
            return False
    except ImportError:
        print("⚠️  PyTorch not installed - cannot validate GPU")
        return False


def create_env_file() -> None:
    """Interactive .env file creation"""
    print("🚀 Diffusion Finetuning Toolkit - Environment Setup")
    print("=" * 60)
    
    # Detect RunPod environment
    runpod_vars = detect_runpod_environment()
    
    # Validate GPU
    gpu_available = validate_gpu()
    
    print("\n📝 Creating .env file...")
    print("Press Enter to use default values, or type your own.")
    
    # Required tokens
    print("\n🔑 AUTHENTICATION TOKENS")
    hf_token = get_user_input(
        "Hugging Face Token (required for model downloads)",
        required=True
    )
    
    wandb_key = get_user_input(
        "Weights & Biases API Key (optional, for experiment tracking)"
    )
    
    # Paths (with RunPod defaults)
    print("\n📁 STORAGE PATHS")
    base_path = get_user_input(
        "Base workspace path",
        default="/workspace" if runpod_vars else "./workspace"
    )
    
    # Training settings
    print("\n🎯 TRAINING SETTINGS")
    batch_size = get_user_input("Default batch size", default="1")
    gradient_accumulation = get_user_input("Gradient accumulation steps", default="8")
    learning_rate = get_user_input("Learning rate", default="1e-4")
    max_steps = get_user_input("Max training steps", default="1000")
    
    # LoRA settings
    print("\n🔧 LORA SETTINGS")
    lora_rank = get_user_input("LoRA rank", default="64")
    lora_alpha = get_user_input("LoRA alpha", default="64")
    lora_dropout = get_user_input("LoRA dropout", default="0.1")
    
    # Auto-captioning
    print("\n🤖 AUTO-CAPTIONING")
    captioning_model = get_user_input("Captioning model", default="blip2")
    quality_threshold = get_user_input("Quality threshold", default="0.7")
    
    # Create .env content
    env_content = f"""# Diffusion Finetuning Toolkit Environment Configuration
# Generated by setup_env.py

# =============================================================================
# AUTHENTICATION TOKENS
# =============================================================================
HF_TOKEN={hf_token}
HUGGINGFACE_HUB_TOKEN={hf_token}
"""
    
    if wandb_key:
        env_content += f"""
WANDB_API_KEY={wandb_key}
WANDB_PROJECT=diffusion-finetuning
"""
    
    env_content += f"""
# =============================================================================
# CACHE AND STORAGE PATHS
# =============================================================================
HF_HOME={base_path}/cache/huggingface
TRANSFORMERS_CACHE={base_path}/cache/transformers
DIFFUSERS_CACHE={base_path}/cache/diffusers
HF_DATASETS_CACHE={base_path}/cache/datasets
MODEL_CACHE_DIR={base_path}/cache/models
DATA_DIR={base_path}/data
OUTPUT_DIR={base_path}/outputs
LOGS_DIR={base_path}/logs
TEMP_DIR={base_path}/tmp

# =============================================================================
# PYTORCH AND CUDA OPTIMIZATION (A40 Optimized)
# =============================================================================
CUDA_VISIBLE_DEVICES=0
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512,expandable_segments:True
TORCH_DTYPE=bfloat16
MIXED_PRECISION=bf16
TOKENIZERS_PARALLELISM=false
OMP_NUM_THREADS=8

# =============================================================================
# MODEL PATHS
# =============================================================================
FLUX_MODEL_PATH=black-forest-labs/FLUX.1-dev
WAN21_I2V_MODEL_PATH=Wan-AI/Wan2.1-I2V-14B-720P-Diffusers
WAN21_T2V_MODEL_PATH=Wan-AI/Wan2.1-T2V-14B-720P-Diffusers

# =============================================================================
# TRAINING CONFIGURATION
# =============================================================================
DEFAULT_BATCH_SIZE={batch_size}
DEFAULT_GRADIENT_ACCUMULATION_STEPS={gradient_accumulation}
DEFAULT_LEARNING_RATE={learning_rate}
DEFAULT_MAX_STEPS={max_steps}

# LoRA Settings
DEFAULT_LORA_RANK={lora_rank}
DEFAULT_LORA_ALPHA={lora_alpha}
DEFAULT_LORA_DROPOUT={lora_dropout}

# Memory Settings for A40
MAX_MEMORY_GB=45
GRADIENT_CHECKPOINTING=true
USE_MEMORY_EFFICIENT_ATTENTION=true

# =============================================================================
# AUTO-CAPTIONING
# =============================================================================
CAPTIONING_MODEL={captioning_model}
IMAGE_QUALITY_THRESHOLD={quality_threshold}
CAPTIONING_BATCH_SIZE=4

# =============================================================================
# DATASET PROCESSING
# =============================================================================
DEFAULT_IMAGE_RESOLUTION=1024
DEFAULT_VIDEO_RESOLUTION=720,1280
DEFAULT_NUM_FRAMES=16
DATALOADER_NUM_WORKERS=4
PIN_MEMORY=true

# =============================================================================
# MONITORING AND LOGGING
# =============================================================================
LOG_LEVEL=INFO
LOGGING_STEPS=10
VALIDATION_STEPS=500
CHECKPOINTING_STEPS=500

# =============================================================================
# PERFORMANCE OPTIMIZATION
# =============================================================================
USE_FLASH_ATTENTION=true
USE_XFORMERS=true
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1

# =============================================================================
# SAFETY AND BACKUP
# =============================================================================
AUTO_BACKUP=true
BACKUP_INTERVAL_STEPS=1000
MAX_CHECKPOINTS=5
"""
    
    # Add RunPod specific variables
    if runpod_vars:
        env_content += "\n# =============================================================================\n"
        env_content += "# RUNPOD ENVIRONMENT\n"
        env_content += "# =============================================================================\n"
        for key, value in runpod_vars.items():
            env_content += f"{key}={value}\n"
    
    # Write .env file
    with open(".env", "w") as f:
        f.write(env_content)
    
    print(f"\n✅ .env file created successfully!")
    print(f"📁 Location: {os.path.abspath('.env')}")
    
    # Create directories
    print(f"\n📁 Creating directories...")
    create_directories(base_path)
    
    print(f"\n🎉 Environment setup complete!")
    print(f"\nNext steps:")
    print(f"1. Review and edit .env file if needed")
    print(f"2. Install dependencies: pip install -r requirements_toolkit.txt")
    print(f"3. Test setup: python -c 'from toolkit.core import *; print(\"✅ Toolkit ready!\")'")
    print(f"4. Start training: python toolkit_cli.py train --config configs/flux_config.yaml")


def validate_env_file() -> bool:
    """Validate existing .env file"""
    if not os.path.exists(".env"):
        print("❌ .env file not found!")
        return False
    
    required_vars = [
        "HF_TOKEN",
        "HF_HOME", 
        "DATA_DIR",
        "OUTPUT_DIR"
    ]
    
    missing_vars = []
    
    # Load .env file
    with open(".env", "r") as f:
        env_content = f.read()
    
    for var in required_vars:
        if f"{var}=" not in env_content:
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing required variables: {', '.join(missing_vars)}")
        return False
    
    print("✅ .env file validation passed!")
    return True


def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1] == "validate":
        validate_env_file()
    else:
        create_env_file()


if __name__ == "__main__":
    main()
