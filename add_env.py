#!/usr/bin/env python3
"""
Simple script to add .env file with your tokens
Just run: python add_env.py
"""

import os
from pathlib import Path


def add_env_file():
    """Add .env file with user tokens"""
    
    print("🔑 Add your tokens to create .env file")
    print("=" * 50)
    
    # Get Hugging Face token
    print("\n1. Hugging Face Token (REQUIRED)")
    print("   Get from: https://huggingface.co/settings/tokens")
    hf_token = input("   Enter HF token (hf_...): ").strip()
    
    # Validate HF token
    if not hf_token or not hf_token.startswith('hf_'):
        print("❌ Invalid Hugging Face token! Must start with 'hf_'")
        return
    
    # Get W&B token (optional)
    print("\n2. Weights & Biases Token (OPTIONAL)")
    print("   Get from: https://wandb.ai/authorize")
    print("   Press Enter to skip")
    wandb_token = input("   Enter W&B token: ").strip()
    
    # Create .env content
    env_content = f"""# Diffusion Finetuning Toolkit - Environment Variables
# Generated by add_env.py

# =============================================================================
# AUTHENTICATION TOKENS
# =============================================================================
HF_TOKEN={hf_token}
HUGGINGFACE_HUB_TOKEN={hf_token}
"""
    
    if wandb_token:
        env_content += f"""
# Weights & Biases
WANDB_API_KEY={wandb_token}
WANDB_PROJECT=diffusion-finetuning
"""
    
    env_content += """
# =============================================================================
# STORAGE PATHS (RunPod Optimized)
# =============================================================================
HF_HOME=/workspace/cache/huggingface
TRANSFORMERS_CACHE=/workspace/cache/transformers
DIFFUSERS_CACHE=/workspace/cache/diffusers
HF_DATASETS_CACHE=/workspace/cache/datasets
MODEL_CACHE_DIR=/workspace/cache/models
DATA_DIR=/workspace/data
OUTPUT_DIR=/workspace/outputs
LOGS_DIR=/workspace/logs
TEMP_DIR=/workspace/tmp

# =============================================================================
# CUDA OPTIMIZATION (A40 GPU)
# =============================================================================
CUDA_VISIBLE_DEVICES=0
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512,expandable_segments:True
TORCH_DTYPE=bfloat16
MIXED_PRECISION=bf16
TOKENIZERS_PARALLELISM=false

# =============================================================================
# MODEL PATHS
# =============================================================================
FLUX_MODEL_PATH=black-forest-labs/FLUX.1-dev
WAN21_I2V_MODEL_PATH=Wan-AI/Wan2.1-I2V-14B-720P-Diffusers
WAN21_T2V_MODEL_PATH=Wan-AI/Wan2.1-T2V-14B-720P-Diffusers

# =============================================================================
# TRAINING DEFAULTS (A40 Optimized)
# =============================================================================
DEFAULT_BATCH_SIZE=1
DEFAULT_GRADIENT_ACCUMULATION_STEPS=8
DEFAULT_LEARNING_RATE=1e-4
DEFAULT_MAX_STEPS=1000

# LoRA Settings
DEFAULT_LORA_RANK=64
DEFAULT_LORA_ALPHA=64
DEFAULT_LORA_DROPOUT=0.1

# Memory Settings
MAX_MEMORY_GB=45
GRADIENT_CHECKPOINTING=true
USE_MEMORY_EFFICIENT_ATTENTION=true

# =============================================================================
# AUTO-CAPTIONING
# =============================================================================
CAPTIONING_MODEL=blip2
CAPTIONING_BATCH_SIZE=4
IMAGE_QUALITY_THRESHOLD=0.7

# =============================================================================
# PERFORMANCE OPTIMIZATION
# =============================================================================
OMP_NUM_THREADS=8
DATALOADER_NUM_WORKERS=4
PIN_MEMORY=true
USE_FLASH_ATTENTION=true
USE_XFORMERS=true
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1

# =============================================================================
# MONITORING
# =============================================================================
LOG_LEVEL=INFO
LOGGING_STEPS=10
VALIDATION_STEPS=500
CHECKPOINTING_STEPS=500

# =============================================================================
# BACKUP AND SAFETY
# =============================================================================
AUTO_BACKUP=true
BACKUP_INTERVAL_STEPS=1000
MAX_CHECKPOINTS=5
"""
    
    # Write .env file
    with open('.env', 'w') as f:
        f.write(env_content)
    
    print(f"\n✅ .env file created successfully!")
    print(f"📁 Location: {os.path.abspath('.env')}")
    
    # Create directories
    print(f"\n📁 Creating workspace directories...")
    create_workspace_dirs()
    
    print(f"\n🎉 Setup complete!")
    print(f"\nYour .env file contains:")
    print(f"  ✅ Hugging Face token: {hf_token[:10]}...")
    if wandb_token:
        print(f"  ✅ W&B token: {wandb_token[:10]}...")
    print(f"  ✅ A40 GPU optimizations")
    print(f"  ✅ RunPod workspace paths")
    print(f"  ✅ Training defaults")
    
    print(f"\nNext steps:")
    print(f"1. Install toolkit: pip install -r requirements_toolkit.txt")
    print(f"2. Test setup: python -c 'import torch; print(f\"GPU: {{torch.cuda.get_device_name(0)}}\")'")
    print(f"3. Start training: python toolkit_cli.py train --config configs/flux_config.yaml")


def create_workspace_dirs():
    """Create workspace directories"""
    dirs = [
        "/workspace/cache/huggingface",
        "/workspace/cache/transformers", 
        "/workspace/cache/diffusers",
        "/workspace/cache/datasets",
        "/workspace/cache/models",
        "/workspace/data",
        "/workspace/outputs",
        "/workspace/logs",
        "/workspace/tmp"
    ]
    
    for dir_path in dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        print(f"  ✅ {dir_path}")


def check_existing_env():
    """Check if .env already exists"""
    if os.path.exists('.env'):
        print("⚠️  .env file already exists!")
        choice = input("Overwrite? (y/N): ").strip().lower()
        return choice == 'y'
    return True


def main():
    """Main function"""
    if not check_existing_env():
        print("❌ Cancelled - keeping existing .env file")
        return
    
    try:
        add_env_file()
    except KeyboardInterrupt:
        print("\n❌ Cancelled by user")
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    main()
