# Production Requirements for Diffusion Model Training
# Core dependencies for both Wan2.1 and FLUX models

# Core ML Libraries
torch>=2.4.0
torchvision>=0.19.0
torchaudio>=2.4.0

# Diffusion Models
diffusers>=0.31.0
transformers>=4.40.0
accelerate>=0.28.0

# LoRA and Quantization
peft>=0.10.0
bitsandbytes>=0.43.0

# Image and Video Processing
opencv-python>=4.8.0
imageio>=2.31.0
imageio-ffmpeg>=0.4.8
decord>=0.6.0
Pillow>=10.0.0

# Audio Processing (for video)
librosa>=0.10.0
soundfile>=0.12.0

# Configuration Management
omegaconf>=2.3.0
pyyaml>=6.0
python-dotenv>=1.0.0

# Monitoring and Logging
wandb>=0.16.0
tensorboard>=2.15.0
tqdm>=4.66.0
psutil>=5.9.0

# Data Processing
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.11.0

# HTTP and API
requests>=2.31.0
urllib3>=2.0.0

# Utilities
pathlib2>=2.3.7
typing-extensions>=4.8.0
packaging>=23.0

# Memory Optimization
xformers>=0.0.22

# Development and Testing
pytest>=7.4.0
pytest-cov>=4.1.0
black>=23.0.0
flake8>=6.0.0
isort>=5.12.0

# Jupyter (for notebooks)
jupyter>=1.0.0
ipywidgets>=8.0.0

# Cloud Storage (optional)
boto3>=1.34.0  # AWS S3
google-cloud-storage>=2.10.0  # Google Cloud
azure-storage-blob>=12.19.0  # Azure Blob

# Notification Services (optional)
slack-sdk>=3.26.0
discord-webhook>=1.3.0

# Email Support
secure-smtplib>=0.1.1

# Performance Profiling
py-spy>=0.3.14
memory-profiler>=0.61.0

# Security
cryptography>=41.0.0
keyring>=24.0.0

# Database (for metadata)
sqlalchemy>=2.0.0
sqlite3  # Built-in with Python

# Serialization
pickle5>=0.0.12  # For Python < 3.8 compatibility
joblib>=1.3.0

# Progress Bars and UI
rich>=13.0.0
click>=8.1.0

# Scientific Computing
scikit-learn>=1.3.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Video Processing
moviepy>=1.0.3
ffmpeg-python>=0.2.0

# Text Processing
tokenizers>=0.15.0
sentencepiece>=0.1.99

# Model Serving (optional)
fastapi>=0.104.0
uvicorn>=0.24.0
gradio>=4.0.0

# Distributed Training
deepspeed>=0.12.0  # Optional for large-scale training

# MLOps
mlflow>=2.8.0  # Alternative to W&B
dvc>=3.0.0  # Data version control

# System Monitoring
nvidia-ml-py>=12.535.0  # NVIDIA GPU monitoring
gpustat>=1.1.0

# Configuration Validation
cerberus>=1.3.4
jsonschema>=4.19.0

# Async Support
aiohttp>=3.9.0
asyncio>=3.4.3

# Caching
diskcache>=5.6.0
redis>=5.0.0  # Optional for distributed caching

# Compression
lz4>=4.3.0
zstandard>=0.22.0

# Environment Management
conda-pack>=0.7.0  # For conda environment packaging
pip-tools>=7.3.0  # For dependency management
