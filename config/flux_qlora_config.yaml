# FLUX.1-dev QLoRA (4-bit) Finetuning Configuration
# Memory-efficient training for consumer GPUs

model:
  name: "flux-dev-qlora"
  pretrained_model_name_or_path: "black-forest-labs/FLUX.1-dev"
  revision: null
  variant: null
  cache_dir: "./cache"
  
  # QLoRA configuration for FLUX
  use_lora: true
  lora_type: "qlora"
  lora_rank: 16  # Lower rank for QLoRA
  lora_alpha: 16
  lora_dropout: 0.05
  lora_target_modules: ["to_q", "to_k", "to_v", "to_out.0", "ff.net.0.proj", "ff.net.2"]
  
  # 4-bit quantization settings
  use_4bit: true
  bnb_4bit_compute_dtype: "bfloat16"
  bnb_4bit_quant_type: "nf4"
  bnb_4bit_use_double_quant: true
  bnb_4bit_quant_storage: "uint8"
  
  # Memory optimization
  load_in_4bit: true
  device_map: "auto"
  max_memory: null
  
training:
  output_dir: "./outputs/flux-qlora-finetuned"
  logging_dir: "./logs"
  cache_dir: "./cache"
  seed: 42
  
  # Image parameters (reduced for memory efficiency)
  resolution: 768  # Reduced from 1024 for memory savings
  width: 768
  height: 768
  
  # Training parameters optimized for QLoRA
  train_batch_size: 1
  gradient_accumulation_steps: 8  # Higher accumulation for effective batch size
  num_train_epochs: 150
  max_train_steps: 6000  # More steps for QLoRA
  learning_rate: 3e-4  # Higher LR for QLoRA
  scale_lr: false
  lr_scheduler: "cosine"
  lr_warmup_steps: 500
  lr_num_cycles: 1
  
  # Optimization for QLoRA
  use_8bit_adam: true
  adam_beta1: 0.9
  adam_beta2: 0.999
  adam_weight_decay: 0.01
  adam_epsilon: 1e-08
  max_grad_norm: 0.3
  
  # Memory optimization
  gradient_checkpointing: true
  mixed_precision: "bf16"
  allow_tf32: true
  use_ema: false
  enable_xformers_memory_efficient_attention: true
  
  # Data loading (reduced for memory)
  dataloader_num_workers: 2
  pin_memory: false
  
  # Checkpointing
  checkpointing_steps: 250
  checkpoints_total_limit: 2
  resume_from_checkpoint: null
  
  # Validation
  validation_steps: 750
  validation_epochs: 15
  num_validation_images: 2
  
  # Logging and monitoring
  report_to: "wandb"
  logging_steps: 25
  save_steps: 250
  
dataset:
  train_data_dir: "./data/train"
  validation_data_dir: "./data/validation"
  
  # Data format
  image_column: "image"
  caption_column: "caption"
  
  # Text processing (reduced for memory)
  max_sequence_length: 256
  tokenizer_max_length: 256
  
  # Image processing
  center_crop: true
  random_flip: 0.5
  normalize: true
  
  # Preprocessing
  preprocessing_num_workers: 2
  
validation:
  validation_prompts:
    - "A beautiful landscape in the style"
    - "A portrait in artistic style"
  negative_prompt: "blurry, low quality, distorted, ugly, deformed"
  num_validation_images: 2
  validation_epochs: 15
  guidance_scale: 3.5
  num_inference_steps: 20  # Fewer steps for faster validation
  
wandb:
  project_name: "flux-qlora-finetuning"
  run_name: "flux-dev-qlora-4bit"
  tags: ["flux", "qlora", "4bit", "memory-efficient"]
  notes: "FLUX.1-dev QLoRA 4-bit finetuning for consumer GPUs"

# FLUX QLoRA specific settings
flux:
  # Training technique
  training_type: "dreambooth"
  
  # Dreambooth settings
  instance_prompt: "a photo of sks style"
  class_prompt: "a photo"
  prior_preservation: true
  prior_preservation_class_weight: 1.0
  num_class_images: 50  # Reduced for memory
  
  # Text encoder training (disabled for QLoRA)
  train_text_encoder: false
  
  # Advanced settings
  snr_gamma: 5.0
  prediction_type: "epsilon"
  
  # Memory optimization
  cpu_offload: false
  sequential_cpu_offload: false
  
# QLoRA specific optimizations
qlora:
  # Memory management
  gradient_checkpointing_steps: 1
  save_safetensors: true
  
  # Quantization monitoring
  log_quantization_stats: true
  monitor_memory_usage: true
