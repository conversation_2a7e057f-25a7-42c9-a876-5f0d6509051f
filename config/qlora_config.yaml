# Wan2.1 QLoRA (4-bit) Finetuning Configuration
# Optimized for consumer GPUs (12-24GB VRAM)

model:
  name: "wan2.1-i2v-qlora"
  pretrained_model_name_or_path: "Wan-AI/Wan2.1-I2V-14B-720P-Diffusers"
  revision: null
  variant: null
  cache_dir: "./cache"
  
  # QLoRA configuration - 4-bit quantization with LoRA
  use_lora: true
  lora_type: "qlora"
  lora_rank: 32  # Lower rank for QLoRA to balance quality vs memory
  lora_alpha: 32
  lora_dropout: 0.05
  lora_target_modules: ["to_q", "to_k", "to_v", "to_out.0", "proj_in", "proj_out", "ff.net.0.proj", "ff.net.2"]
  
  # 4-bit quantization settings
  use_4bit: true
  bnb_4bit_compute_dtype: "bfloat16"
  bnb_4bit_quant_type: "nf4"  # NormalFloat4 - better than FP4
  bnb_4bit_use_double_quant: true  # Nested quantization for extra memory savings
  bnb_4bit_quant_storage: "uint8"
  
  # Memory optimization
  load_in_4bit: true
  device_map: "auto"
  max_memory: null  # Let it auto-detect
  
training:
  output_dir: "./outputs/wan2.1-qlora-finetuned"
  logging_dir: "./logs"
  cache_dir: "./cache"
  seed: 42
  
  # Video parameters (reduced for memory efficiency)
  resolution: 576  # Lower resolution for QLoRA
  width: 1024
  height: 576
  num_frames: 49   # Fewer frames for memory efficiency
  frame_rate: 16
  
  # Training parameters optimized for QLoRA
  train_batch_size: 1
  gradient_accumulation_steps: 16  # Higher accumulation for effective batch size
  num_train_epochs: 100
  max_train_steps: 10000
  learning_rate: 2e-4  # Higher LR for QLoRA
  scale_lr: false
  lr_scheduler: "cosine"
  lr_warmup_steps: 1000
  lr_num_cycles: 1
  
  # Optimization for QLoRA
  use_8bit_adam: true  # 8-bit Adam for memory savings
  adam_beta1: 0.9
  adam_beta2: 0.999
  adam_weight_decay: 0.01
  adam_epsilon: 1e-08
  max_grad_norm: 0.3  # Lower gradient clipping for stability
  
  # Memory optimization
  gradient_checkpointing: true
  mixed_precision: "bf16"
  allow_tf32: true
  use_ema: false
  enable_xformers_memory_efficient_attention: true
  
  # Data loading
  dataloader_num_workers: 2  # Reduced for memory
  pin_memory: false  # Disable for memory savings
  
  # Checkpointing
  checkpointing_steps: 250
  checkpoints_total_limit: 2  # Keep fewer checkpoints
  resume_from_checkpoint: null
  
  # Validation
  validation_steps: 500
  validation_epochs: 10
  num_validation_videos: 1  # Single validation video
  
  # Logging and monitoring
  report_to: "wandb"
  logging_steps: 25
  save_steps: 250
  
dataset:
  train_data_dir: "./data/train"
  validation_data_dir: "./data/validation"
  
  # Data format
  image_column: "image"
  video_column: "video"
  caption_column: "caption"
  
  # Text processing
  max_sequence_length: 128  # Shorter sequences for memory
  tokenizer_max_length: 128
  
  # Video processing (optimized for QLoRA)
  video_length: 49
  sample_stride: 2  # Skip frames for memory efficiency
  sample_n_frames: 49
  
  # Data augmentation
  random_flip: 0.5
  center_crop: true
  normalize: true
  
  # Preprocessing
  preprocessing_num_workers: 2
  
validation:
  validation_prompt: "A beautiful landscape with gentle movement"
  negative_prompt: "blurry, low quality, distorted, static, ugly, deformed"
  num_validation_videos: 1
  validation_epochs: 10
  guidance_scale: 5.0
  num_inference_steps: 25  # Fewer steps for faster validation
  
wandb:
  project_name: "wan2.1-qlora-finetuning"
  run_name: "wan2.1-qlora-4bit"
  tags: ["wan2.1", "qlora", "4bit", "memory-efficient"]
  notes: "QLoRA 4-bit finetuning for consumer GPUs"

# QLoRA specific optimizations
qlora:
  # Memory management
  cpu_offload: false
  sequential_cpu_offload: false
  
  # Training stability
  gradient_checkpointing_steps: 1
  save_safetensors: true
  
  # Quantization monitoring
  log_quantization_stats: true
  monitor_memory_usage: true
