# Wan2.1 Variable Size Image-to-Video Finetuning Configuration
# Supports variable-sized images without cropping

model:
  name: "wan2.1-i2v-variable-size"
  pretrained_model_name_or_path: "Wan-AI/Wan2.1-I2V-14B-720P-Diffusers"
  revision: null
  variant: null
  cache_dir: "./cache"
  
  # LoRA configuration
  use_lora: true
  lora_type: "lora"
  lora_rank: 64
  lora_alpha: 64
  lora_dropout: 0.1
  lora_target_modules: ["to_q", "to_k", "to_v", "to_out.0", "proj_in", "proj_out", "ff.net.0.proj", "ff.net.2"]
  
  # Quantization (optional)
  use_8bit: false
  use_4bit: false
  
training:
  output_dir: "./outputs/wan2.1-variable-size-finetuned"
  logging_dir: "./logs"
  cache_dir: "./cache"
  seed: 42
  
  # Variable size parameters
  max_width: 1280
  max_height: 720
  num_frames: 81
  frame_rate: 16
  
  # Aspect ratio buckets for efficient batching
  bucket_sizes:
    - [512, 512]   # Square
    - [576, 576]
    - [640, 640]
    - [704, 704]
    - [768, 768]
    - [832, 832]
    - [896, 896]
    - [960, 960]
    - [1024, 1024]
    - [512, 768]   # Portrait
    - [576, 864]
    - [640, 960]
    - [704, 1056]
    - [768, 512]   # Landscape
    - [864, 576]
    - [960, 640]
    - [1056, 704]
    - [1280, 720]  # 16:9
    - [1024, 576]
    - [960, 540]
    - [854, 480]
  
  # Training parameters
  train_batch_size: 1
  gradient_accumulation_steps: 8
  num_train_epochs: 50
  max_train_steps: 5000
  learning_rate: 1e-5
  scale_lr: false
  lr_scheduler: "cosine"
  lr_warmup_steps: 500
  lr_num_cycles: 1
  
  # Optimization
  use_8bit_adam: false
  adam_beta1: 0.9
  adam_beta2: 0.999
  adam_weight_decay: 1e-2
  adam_epsilon: 1e-08
  max_grad_norm: 1.0
  
  # Memory optimization
  gradient_checkpointing: true
  mixed_precision: "bf16"
  allow_tf32: true
  use_ema: false
  enable_xformers_memory_efficient_attention: true
  
  # Data loading with variable size support
  dataloader_num_workers: 4
  pin_memory: true
  use_bucket_sampler: true
  use_aspect_ratio_grouping: false
  
  # Checkpointing
  checkpointing_steps: 500
  checkpoints_total_limit: 3
  resume_from_checkpoint: null
  
  # Validation
  validation_steps: 250
  validation_epochs: 5
  num_validation_videos: 2
  
  # Logging and monitoring
  report_to: "wandb"
  logging_steps: 10
  save_steps: 500
  
dataset:
  train_data_dir: "./data/train"
  validation_data_dir: "./data/validation"
  
  # Data format
  image_column: "image"
  video_column: "video"
  caption_column: "caption"
  
  # Text processing
  max_sequence_length: 256
  tokenizer_max_length: 256
  
  # Video processing
  video_length: 81
  sample_stride: 1
  sample_n_frames: 81
  
  # Variable size processing
  resize_mode: "pad"  # Options: "pad", "crop", "stretch"
  random_flip: 0.5
  normalize: true
  
  # Preprocessing
  preprocessing_num_workers: 8
  
validation:
  validation_prompt: "A serene landscape with flowing water and gentle movement"
  negative_prompt: "blurry, low quality, distorted, static, ugly, deformed"
  num_validation_videos: 2
  validation_epochs: 5
  guidance_scale: 5.0
  num_inference_steps: 50
  
wandb:
  project_name: "wan2.1-variable-size-finetuning"
  run_name: "wan2.1-variable-size-experiment"
  tags: ["wan2.1", "variable-size", "aspect-ratio-preservation"]
  notes: "Variable size training without cropping for Wan2.1"

# Variable size specific settings
variable_size:
  # Aspect ratio preservation
  preserve_aspect_ratio: true
  pad_color: [0, 0, 0]  # RGB values for padding
  
  # Bucket optimization
  enable_bucketing: true
  bucket_tolerance: 0.1  # Aspect ratio tolerance for grouping
  min_bucket_size: 2     # Minimum samples per bucket
  
  # Dynamic resizing
  dynamic_resolution: false  # Enable dynamic resolution during training
  resolution_steps: [512, 640, 768, 896, 1024]  # Progressive resolution training
  
  # Memory optimization for variable sizes
  max_pixels_per_batch: 2073600  # 1280*720*2 - adjust based on GPU memory
  adaptive_batch_size: false     # Automatically adjust batch size based on resolution
