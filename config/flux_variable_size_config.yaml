# FLUX.1-dev Variable Size Text-to-Image Finetuning Configuration
# Supports variable-sized images without cropping

model:
  name: "flux-dev-variable-size"
  pretrained_model_name_or_path: "black-forest-labs/FLUX.1-dev"
  revision: null
  variant: null
  cache_dir: "./cache"
  
  # LoRA configuration for FLUX
  use_lora: true
  lora_type: "lora"
  lora_rank: 16
  lora_alpha: 16
  lora_dropout: 0.1
  lora_target_modules: ["to_q", "to_k", "to_v", "to_out.0", "ff.net.0.proj", "ff.net.2"]
  
  # FLUX specific settings
  guidance_scale: 3.5
  num_inference_steps: 28
  
  # Quantization (optional)
  use_8bit: false
  use_4bit: false
  
training:
  output_dir: "./outputs/flux-variable-size-finetuned"
  logging_dir: "./logs"
  cache_dir: "./cache"
  seed: 42
  
  # Variable size parameters
  max_resolution: 1024
  
  # Square bucket sizes for FLUX (maintains square aspect ratio)
  bucket_sizes: [512, 576, 640, 704, 768, 832, 896, 960, 1024]
  
  # Training parameters optimized for variable size
  train_batch_size: 1
  gradient_accumulation_steps: 4
  num_train_epochs: 100
  max_train_steps: 4000
  learning_rate: 2e-4
  scale_lr: false
  lr_scheduler: "constant"
  lr_warmup_steps: 0
  lr_num_cycles: 1
  
  # Optimization
  use_8bit_adam: false
  adam_beta1: 0.9
  adam_beta2: 0.999
  adam_weight_decay: 1e-2
  adam_epsilon: 1e-08
  max_grad_norm: 1.0
  
  # Memory optimization
  gradient_checkpointing: true
  mixed_precision: "bf16"
  allow_tf32: true
  use_ema: false
  enable_xformers_memory_efficient_attention: true
  
  # Data loading with variable size support
  dataloader_num_workers: 4
  pin_memory: true
  use_bucket_sampler: true
  use_aspect_ratio_grouping: false
  
  # Checkpointing
  checkpointing_steps: 500
  checkpoints_total_limit: 3
  resume_from_checkpoint: null
  
  # Validation
  validation_steps: 500
  validation_epochs: 10
  num_validation_images: 4
  
  # Logging and monitoring
  report_to: "wandb"
  logging_steps: 50
  save_steps: 500
  
dataset:
  train_data_dir: "./data/train"
  validation_data_dir: "./data/validation"
  
  # Data format
  image_column: "image"
  caption_column: "caption"
  
  # Text processing
  max_sequence_length: 512
  tokenizer_max_length: 512
  
  # Variable size processing
  resize_mode: "pad"  # Options: "pad", "crop", "stretch"
  random_flip: 0.5
  normalize: true
  
  # Preprocessing
  preprocessing_num_workers: 8
  
validation:
  validation_prompts:
    - "A beautiful landscape with mountains and a lake"
    - "A portrait of a person in artistic style"
    - "An abstract geometric pattern"
    - "A futuristic cityscape at sunset"
  negative_prompt: "blurry, low quality, distorted, ugly, deformed"
  num_validation_images: 4
  validation_epochs: 10
  guidance_scale: 3.5
  num_inference_steps: 28
  
wandb:
  project_name: "flux-variable-size-finetuning"
  run_name: "flux-dev-variable-size-experiment"
  tags: ["flux", "variable-size", "aspect-ratio-preservation"]
  notes: "Variable size training without cropping for FLUX.1-dev"

# FLUX specific settings
flux:
  # Training technique
  training_type: "text_to_image"  # Options: "dreambooth", "text_to_image"
  
  # Dreambooth settings (if using dreambooth)
  instance_prompt: "a photo of sks style"
  class_prompt: "a photo"
  prior_preservation: true
  prior_preservation_class_weight: 1.0
  num_class_images: 100
  
  # Text encoder training
  train_text_encoder: false
  text_encoder_lr: 5e-6
  
  # Advanced settings
  snr_gamma: 5.0
  prediction_type: "epsilon"
  
  # Memory optimization
  cpu_offload: false
  sequential_cpu_offload: false

# Variable size specific settings
variable_size:
  # Aspect ratio preservation
  preserve_aspect_ratio: true
  pad_color: [0, 0, 0]  # RGB values for padding
  
  # Bucket optimization for FLUX (square buckets)
  enable_bucketing: true
  bucket_tolerance: 0.05  # Tighter tolerance for square buckets
  min_bucket_size: 2      # Minimum samples per bucket
  
  # Dynamic resizing
  dynamic_resolution: false  # Enable dynamic resolution during training
  resolution_steps: [512, 640, 768, 896, 1024]  # Progressive resolution training
  
  # Memory optimization for variable sizes
  max_pixels_per_batch: 1048576  # 1024*1024 - adjust based on GPU memory
  adaptive_batch_size: false     # Automatically adjust batch size based on resolution
  
  # FLUX specific optimizations
  square_crop_probability: 0.3   # Probability of using square crop vs padding
  center_crop_probability: 0.7   # Probability of center crop vs random crop
