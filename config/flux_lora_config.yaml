# FLUX.1-dev LoRA Finetuning Configuration
# Optimized for text-to-image generation

model:
  name: "flux-dev-lora"
  pretrained_model_name_or_path: "black-forest-labs/FLUX.1-dev"
  revision: null
  variant: null
  cache_dir: "./cache"
  
  # LoRA configuration for FLUX
  use_lora: true
  lora_type: "lora"
  lora_rank: 16  # Start with 16 for style LoRAs, 32-64 for complex concepts
  lora_alpha: 16
  lora_dropout: 0.1
  lora_target_modules: ["to_q", "to_k", "to_v", "to_out.0", "ff.net.0.proj", "ff.net.2"]
  
  # FLUX specific settings
  guidance_scale: 3.5  # FLUX works well with lower guidance
  num_inference_steps: 28  # FLUX typically uses fewer steps
  
  # Quantization (optional for memory efficiency)
  use_8bit: false
  use_4bit: false
  
training:
  output_dir: "./outputs/flux-lora-finetuned"
  logging_dir: "./logs"
  cache_dir: "./cache"
  seed: 42
  
  # Image parameters for FLUX
  resolution: 1024  # FLUX was trained on 1024x1024
  width: 1024
  height: 1024
  
  # Training parameters optimized for FLUX LoRA
  train_batch_size: 1
  gradient_accumulation_steps: 4
  num_train_epochs: 100
  max_train_steps: 4000  # FLUX LoRA typically needs fewer steps
  learning_rate: 2e-4  # Higher LR for LoRA
  scale_lr: false
  lr_scheduler: "constant"
  lr_warmup_steps: 0
  lr_num_cycles: 1
  
  # Optimization
  use_8bit_adam: false
  adam_beta1: 0.9
  adam_beta2: 0.999
  adam_weight_decay: 1e-2
  adam_epsilon: 1e-08
  max_grad_norm: 1.0
  
  # Memory optimization
  gradient_checkpointing: true
  mixed_precision: "bf16"
  allow_tf32: true
  use_ema: false
  enable_xformers_memory_efficient_attention: true
  
  # Data loading
  dataloader_num_workers: 4
  pin_memory: true
  
  # Checkpointing
  checkpointing_steps: 500
  checkpoints_total_limit: 3
  resume_from_checkpoint: null
  
  # Validation
  validation_steps: 500
  validation_epochs: 10
  num_validation_images: 4
  
  # Logging and monitoring
  report_to: "wandb"
  logging_steps: 50
  save_steps: 500
  
dataset:
  train_data_dir: "./data/train"
  validation_data_dir: "./data/validation"
  
  # Data format
  image_column: "image"
  caption_column: "caption"
  
  # Text processing
  max_sequence_length: 512  # FLUX supports longer prompts
  tokenizer_max_length: 512
  
  # Image processing
  center_crop: true
  random_flip: 0.5
  normalize: true
  
  # Preprocessing
  preprocessing_num_workers: 8
  
validation:
  validation_prompts:
    - "A beautiful landscape with mountains and a lake"
    - "A portrait of a person in artistic style"
    - "An abstract geometric pattern"
    - "A futuristic cityscape at sunset"
  negative_prompt: "blurry, low quality, distorted, ugly, deformed"
  num_validation_images: 4
  validation_epochs: 10
  guidance_scale: 3.5
  num_inference_steps: 28
  
wandb:
  project_name: "flux-lora-finetuning"
  run_name: "flux-dev-custom-style"
  tags: ["flux", "lora", "text-to-image", "style"]
  notes: "FLUX.1-dev LoRA finetuning for custom style"

# FLUX specific settings
flux:
  # Training technique
  training_type: "dreambooth"  # Options: "dreambooth", "text_to_image"
  
  # Dreambooth settings (if using dreambooth)
  instance_prompt: "a photo of sks person"  # Replace 'sks' with your identifier
  class_prompt: "a photo of person"
  prior_preservation: true
  prior_preservation_class_weight: 1.0
  num_class_images: 100
  
  # Text encoder training
  train_text_encoder: false  # Set to true for better concept learning
  text_encoder_lr: 5e-6  # Lower LR for text encoder
  
  # Advanced settings
  snr_gamma: 5.0  # Signal-to-noise ratio weighting
  prediction_type: "epsilon"  # FLUX uses epsilon prediction
  
  # Memory optimization
  cpu_offload: false
  sequential_cpu_offload: false
