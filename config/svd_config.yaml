# Stable Video Diffusion Training Configuration

model:
  name: "stable-video-diffusion"
  pretrained_model_name_or_path: "stabilityai/stable-video-diffusion-img2vid-xt"
  revision: null
  variant: null
  
training:
  output_dir: "./outputs/svd-finetuned"
  cache_dir: null
  seed: 42
  resolution: 576
  num_frames: 25
  train_batch_size: 1
  num_train_epochs: 100
  max_train_steps: 10000
  gradient_accumulation_steps: 4
  gradient_checkpointing: true
  learning_rate: 1e-5
  scale_lr: false
  lr_scheduler: "constant"
  lr_warmup_steps: 500
  use_8bit_adam: false
  allow_tf32: true
  use_ema: false
  non_ema_revision: null
  dataloader_num_workers: 8
  adam_beta1: 0.9
  adam_beta2: 0.999
  adam_weight_decay: 1e-2
  adam_epsilon: 1e-08
  max_grad_norm: 1.0
  push_to_hub: false
  hub_token: null
  hub_model_id: null
  logging_dir: "logs"
  mixed_precision: "fp16"
  report_to: "wandb"
  local_rank: -1
  checkpointing_steps: 500
  checkpoints_total_limit: 5
  resume_from_checkpoint: null
  enable_xformers_memory_efficient_attention: true
  noise_offset: 0.0
  validation_steps: 100
  
dataset:
  train_data_dir: "./data/train"
  validation_data_dir: "./data/validation"
  image_column: "image"
  video_column: "video"
  caption_column: "caption"
  max_sequence_length: 77
  id_token: null
  
validation:
  validation_prompt: "A beautiful landscape transforming through seasons"
  num_validation_videos: 4
  validation_epochs: 5

wandb:
  project_name: "image-to-video-finetuning"
  run_name: "svd-custom-dataset"
  tags: ["stable-video-diffusion", "image-to-video", "finetuning"]
