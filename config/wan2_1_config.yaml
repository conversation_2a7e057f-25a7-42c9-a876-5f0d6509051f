# Wan2.1 Image-to-Video Finetuning Configuration

model:
  name: "wan2.1-i2v"
  pretrained_model_name_or_path: "Wan-AI/Wan2.1-I2V-14B-720P-Diffusers"
  revision: null
  variant: null
  cache_dir: "./cache"
  
  # Model components
  transformer_path: null  # Use default from pretrained
  vae_path: null         # Use default from pretrained
  text_encoder_path: null # Use default from pretrained
  image_encoder_path: null # Use default from pretrained
  
  # LoRA configuration
  use_lora: true
  lora_rank: 64
  lora_alpha: 64
  lora_dropout: 0.1
  lora_target_modules: ["to_q", "to_k", "to_v", "to_out.0", "proj_in", "proj_out"]
  
  # Quantization
  use_8bit: false
  use_4bit: false
  
training:
  output_dir: "./outputs/wan2.1-finetuned"
  logging_dir: "./logs"
  cache_dir: "./cache"
  seed: 42
  
  # Video parameters
  resolution: 720
  width: 1280
  height: 720
  num_frames: 81
  frame_rate: 16
  
  # Training parameters
  train_batch_size: 1
  gradient_accumulation_steps: 8
  num_train_epochs: 50
  max_train_steps: 5000
  learning_rate: 1e-5
  scale_lr: false
  lr_scheduler: "cosine"
  lr_warmup_steps: 500
  lr_num_cycles: 1
  
  # Optimization
  use_8bit_adam: false
  adam_beta1: 0.9
  adam_beta2: 0.999
  adam_weight_decay: 1e-2
  adam_epsilon: 1e-08
  max_grad_norm: 1.0
  
  # Memory optimization
  gradient_checkpointing: true
  mixed_precision: "bf16"
  allow_tf32: true
  use_ema: false
  enable_xformers_memory_efficient_attention: true
  
  # Data loading
  dataloader_num_workers: 4
  pin_memory: true
  
  # Checkpointing
  checkpointing_steps: 500
  checkpoints_total_limit: 3
  resume_from_checkpoint: null
  
  # Validation
  validation_steps: 250
  validation_epochs: 5
  num_validation_videos: 2
  
  # Logging and monitoring
  report_to: "wandb"
  logging_steps: 10
  save_steps: 500
  
  # Hub integration
  push_to_hub: false
  hub_token: null
  hub_model_id: null
  
dataset:
  train_data_dir: "./data/train"
  validation_data_dir: "./data/validation"
  
  # Data format
  image_column: "image"
  video_column: "video"
  caption_column: "caption"
  
  # Text processing
  max_sequence_length: 256
  tokenizer_max_length: 256
  
  # Video processing
  video_length: 81
  sample_stride: 1
  sample_n_frames: 81
  
  # Data augmentation
  random_flip: 0.5
  center_crop: true
  normalize: true
  
  # Preprocessing
  preprocessing_num_workers: 8
  
validation:
  validation_prompt: "A serene landscape with flowing water and gentle movement"
  negative_prompt: "blurry, low quality, distorted, static, ugly, deformed"
  num_validation_videos: 2
  validation_epochs: 5
  guidance_scale: 5.0
  num_inference_steps: 50
  
wandb:
  project_name: "wan2.1-i2v-finetuning"
  run_name: "wan2.1-custom-dataset"
  tags: ["wan2.1", "image-to-video", "finetuning", "lora"]
  notes: "Finetuning Wan2.1 I2V model on custom dataset"

# Advanced settings
advanced:
  # Noise settings
  noise_offset: 0.0
  snr_gamma: null
  
  # Training stability
  max_train_samples: null
  max_eval_samples: null
  
  # Memory management
  cpu_offload: false
  sequential_cpu_offload: false
  
  # Debugging
  debug: false
  overfit_one_batch: false
