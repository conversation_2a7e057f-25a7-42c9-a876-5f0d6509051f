# =============================================================================
# DIFFUSION FINETUNING TOOLKIT - ENVIRONMENT CONFIGURATION
# =============================================================================

# =============================================================================
# AUTHENTICATION TOKENS (REQUIRED)
# =============================================================================

# Hugging Face Token (REQUIRED for model downloads)
# Get from: https://huggingface.co/settings/tokens
HF_TOKEN=hf_your_token_here
HUGGINGFACE_HUB_TOKEN=hf_your_token_here

# Weights & Biases (Optional - for experiment tracking)
# Get from: https://wandb.ai/authorize
WANDB_API_KEY=your_wandb_key_here
WANDB_PROJECT=diffusion-finetuning
WANDB_ENTITY=your_username

# TensorBoard (Optional - alternative to W&B)
TENSORBOARD_LOG_DIR=/workspace/logs

# =============================================================================
# CACHE AND STORAGE PATHS
# =============================================================================

# Hugging Face Cache Directory
HF_HOME=/workspace/cache/huggingface
TRANSFORMERS_CACHE=/workspace/cache/transformers
DIFFUSERS_CACHE=/workspace/cache/diffusers
HF_DATASETS_CACHE=/workspace/cache/datasets

# Model Storage
MODEL_CACHE_DIR=/workspace/cache/models
PRETRAINED_MODELS_DIR=/workspace/models

# Data Directories
DATA_DIR=/workspace/data
OUTPUT_DIR=/workspace/outputs
LOGS_DIR=/workspace/logs

# Temporary Directory
TEMP_DIR=/workspace/tmp

# =============================================================================
# PYTORCH AND CUDA OPTIMIZATION
# =============================================================================

# CUDA Settings
CUDA_VISIBLE_DEVICES=0
CUDA_LAUNCH_BLOCKING=0

# PyTorch Memory Management (A40 Optimized)
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512,expandable_segments:True

# Mixed Precision Training
TORCH_DTYPE=bfloat16
MIXED_PRECISION=bf16

# Memory Optimization
TOKENIZERS_PARALLELISM=false
OMP_NUM_THREADS=8

# =============================================================================
# MODEL-SPECIFIC SETTINGS
# =============================================================================

# FLUX Model Settings
FLUX_MODEL_PATH=black-forest-labs/FLUX.1-dev
FLUX_CACHE_DIR=/workspace/cache/flux

# Wan2.1 Model Settings
WAN21_I2V_MODEL_PATH=Wan-AI/Wan2.1-I2V-14B-720P-Diffusers
WAN21_T2V_MODEL_PATH=Wan-AI/Wan2.1-T2V-14B-720P-Diffusers
WAN21_CACHE_DIR=/workspace/cache/wan21

# =============================================================================
# TRAINING CONFIGURATION
# =============================================================================

# Default Training Settings
DEFAULT_BATCH_SIZE=1
DEFAULT_GRADIENT_ACCUMULATION_STEPS=8
DEFAULT_LEARNING_RATE=1e-4
DEFAULT_MAX_STEPS=1000

# LoRA Settings
DEFAULT_LORA_RANK=64
DEFAULT_LORA_ALPHA=64
DEFAULT_LORA_DROPOUT=0.1

# Memory Settings for A40 (48GB VRAM)
MAX_MEMORY_GB=45
GRADIENT_CHECKPOINTING=true
USE_MEMORY_EFFICIENT_ATTENTION=true

# =============================================================================
# AUTO-CAPTIONING SETTINGS
# =============================================================================

# Captioning Model
CAPTIONING_MODEL=blip2
CAPTIONING_DEVICE=cuda
CAPTIONING_BATCH_SIZE=4

# Quality Thresholds
IMAGE_QUALITY_THRESHOLD=0.7
VIDEO_QUALITY_THRESHOLD=0.6

# =============================================================================
# DATASET PROCESSING
# =============================================================================

# Image Processing
DEFAULT_IMAGE_RESOLUTION=1024
MAX_IMAGE_RESOLUTION=2048
MIN_IMAGE_RESOLUTION=512

# Video Processing
DEFAULT_VIDEO_RESOLUTION=720,1280
DEFAULT_NUM_FRAMES=16
DEFAULT_FRAME_RATE=8

# Data Loading
DATALOADER_NUM_WORKERS=4
PIN_MEMORY=true

# =============================================================================
# MONITORING AND LOGGING
# =============================================================================

# Logging Levels
LOG_LEVEL=INFO
ACCELERATE_LOG_LEVEL=INFO

# Monitoring Intervals
LOGGING_STEPS=10
VALIDATION_STEPS=500
CHECKPOINTING_STEPS=500

# =============================================================================
# RUNPOD SPECIFIC SETTINGS
# =============================================================================

# RunPod Workspace
RUNPOD_WORKSPACE=/workspace
RUNPOD_POD_ID=${RUNPOD_POD_ID}

# Network Settings
RUNPOD_PUBLIC_IP=${RUNPOD_PUBLIC_IP}
RUNPOD_TCP_PORT_22=${RUNPOD_TCP_PORT_22}

# =============================================================================
# SECURITY AND SAFETY
# =============================================================================

# Disable certain features for security
DISABLE_TELEMETRY=true
TRANSFORMERS_OFFLINE=false
HF_HUB_OFFLINE=false

# Safety Settings
SAFETY_CHECKER=true
NSFW_FILTER=true

# =============================================================================
# PERFORMANCE OPTIMIZATION
# =============================================================================

# CPU Settings
OMP_NUM_THREADS=8
MKL_NUM_THREADS=8
NUMEXPR_NUM_THREADS=8

# I/O Optimization
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Debug Mode (set to false for production)
DEBUG=false
VERBOSE=false

# Development Tools
JUPYTER_ENABLE=false
JUPYTER_PORT=8888

# =============================================================================
# BACKUP AND RECOVERY
# =============================================================================

# Automatic Backup
AUTO_BACKUP=true
BACKUP_INTERVAL_STEPS=1000
BACKUP_DIR=/workspace/backups

# Checkpoint Management
MAX_CHECKPOINTS=5
SAVE_TOTAL_LIMIT=3

# =============================================================================
# OPTIONAL INTEGRATIONS
# =============================================================================

# Discord Webhook (for training notifications)
DISCORD_WEBHOOK_URL=

# Slack Webhook (for training notifications)
SLACK_WEBHOOK_URL=

# Email Notifications
SMTP_SERVER=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
NOTIFICATION_EMAIL=

# =============================================================================
# EXPERIMENTAL FEATURES
# =============================================================================

# Experimental optimizations
USE_FLASH_ATTENTION=true
USE_XFORMERS=true
COMPILE_MODEL=false

# Advanced Memory Management
USE_CPU_OFFLOAD=false
USE_SEQUENTIAL_CPU_OFFLOAD=false

# =============================================================================
# MODEL DOWNLOAD SETTINGS
# =============================================================================

# Download Settings
HF_HUB_ENABLE_HF_TRANSFER=true
HF_TRANSFER_CONCURRENCY=4

# Resume Downloads
HF_HUB_RESUME_DOWNLOAD=true

# =============================================================================
# END OF CONFIGURATION
# =============================================================================
