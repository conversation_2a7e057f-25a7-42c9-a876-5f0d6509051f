# 🎬🎨 Advanced Diffusion Model Finetuning Suite

This repository provides complete solutions for finetuning state-of-the-art diffusion models:

- **🎬 Wan2.1 Image-to-Video**: Finetune video generation models
- **🎨 FLUX.1-dev Text-to-Image**: Finetune text-to-image models

Both models support LoRA, QLoRA, and DoRA techniques for efficient finetuning with reduced memory requirements.

## Features

### 🎬 Wan2.1 Image-to-Video
- ✅ **Video Generation**: Transform images into high-quality videos
- ✅ **81 Frames**: Generate 5-second videos at 16 FPS
- ✅ **720P Resolution**: High-quality 1280x720 output

### 🎨 FLUX.1-dev Text-to-Image
- ✅ **Text-to-Image**: Generate images from text prompts
- ✅ **High Resolution**: Native 1024x1024 generation
- ✅ **Style Training**: Learn artistic styles and concepts
- ✅ **Dreambooth Support**: Train on specific subjects

### 🔧 Shared Features
- ✅ **Multiple LoRA Techniques**: LoRA, QLoRA (4-bit), and DoRA support
- ✅ **Variable-Size Training**: Train on any image size without cropping
- ✅ **Aspect Ratio Preservation**: Maintains original proportions with intelligent padding
- ✅ **Memory Efficient**: QLoRA enables training on 12GB+ consumer GPUs
- ✅ **Superior Quality**: DoRA often outperforms standard LoRA
- ✅ **Custom Dataset Support**: Easy dataset preparation and loading
- ✅ **Flexible Configuration**: YAML-based configuration system
- ✅ **Validation & Monitoring**: Built-in validation and Weights & Biases integration
- ✅ **Memory Optimization**: Gradient checkpointing, mixed precision, and more
- ✅ **Easy Inference**: Simple scripts for generation
- ✅ **Hardware Analysis**: Automatic hardware detection and optimization recommendations

## Model Information

### 🎬 Wan2.1 Image-to-Video
**Model**: `Wan-AI/Wan2.1-I2V-14B-720P-Diffusers`
- **Model Size**: 14B parameters
- **Resolution**: 720P (1280x720)
- **Frames**: 81 frames (5 seconds at 16 FPS)
- **Architecture**: Diffusion Transformer with Flow Matching

### 🎨 FLUX.1-dev Text-to-Image
**Model**: `black-forest-labs/FLUX.1-dev`
- **Model Size**: 12B parameters
- **Resolution**: 1024x1024 (native)
- **Architecture**: Rectified Flow Transformer
- **Strengths**: Excellent prompt following, high detail

## Installation

1. **Clone the repository**:
```bash
git clone <repository-url>
cd finetune
```

2. **Install dependencies**:
```bash
pip install -r requirements.txt
```

3. **Install additional dependencies** (if needed):
```bash
# For video processing
pip install decord imageio-ffmpeg

# For LoRA support
pip install peft

# For monitoring
pip install wandb
```

## LoRA Techniques Comparison

| Method | Memory Usage | Quality | Speed | Best For |
|--------|-------------|---------|-------|----------|
| **LoRA** | High | Good | Fast | High-end GPUs (24GB+) |
| **QLoRA** | Very Low | Good* | Moderate | Consumer GPUs (12-24GB) |
| **DoRA** | High | Better | Moderate | Quality-focused training |

*QLoRA quality depends on quantization settings and may require hyperparameter tuning.

### Hardware Requirements by Method

| GPU Memory | Recommended Method | Configuration |
|------------|-------------------|---------------|
| 8-12GB | QLoRA only | 4-bit, rank 16-32, 576p |
| 12-20GB | QLoRA or LoRA | 4-bit/bf16, rank 32-64, 720p |
| 20-40GB | LoRA or DoRA | bf16, rank 64-128, 720p |
| 40GB+ | Any method | Optimal settings, rank 128+ |

## 🎨 Variable-Size Training (New!)

Train on images of **any size and aspect ratio** without cropping or distortion!

### Benefits
- ✅ **No Content Loss**: Preserves entire image content
- ✅ **Aspect Ratio Preservation**: Maintains original proportions
- ✅ **Better Quality**: Uses full image information
- ✅ **Memory Efficient**: Intelligent bucketing reduces waste
- ✅ **Real-World Ready**: Works with mixed datasets

### Comparison

| Method | Content Preservation | Aspect Ratio | Memory Usage | Quality |
|--------|---------------------|--------------|--------------|---------|
| **Fixed + Crop** | ❌ Loses content | ✅ Maintained | High | Good |
| **Fixed + Stretch** | ✅ Full content | ❌ Distorted | High | Poor |
| **Variable + Pad** | ✅ Full content | ✅ Maintained | Optimal | Excellent |

### Quick Start Variable-Size
```bash
# Analyze your mixed-size dataset
python examples/variable_size_demo.py

# Train without cropping
./scripts/train_variable_size.sh wan2.1  # For video
./scripts/train_variable_size.sh flux    # For images
```

## Quick Start

### 0. Hardware Analysis (Recommended)

```bash
# Analyze your hardware and get recommendations
python examples/memory_optimization_guide.py
```

### 1. Prepare Your Dataset

#### Option A: From Videos (Extract First Frames)
```bash
python scripts/prepare_dataset.py \
    --mode videos \
    --input_dir /path/to/your/videos \
    --output_dir ./data/train \
    --min_duration 2.0
```

#### Option B: From Image-Video Pairs
```bash
python scripts/prepare_dataset.py \
    --mode pairs \
    --input_dir /path/to/images \
    --videos_dir /path/to/videos \
    --output_dir ./data/train
```

### 2. Edit Captions

After dataset preparation, edit `./data/train/metadata.json` to add proper captions:

```json
[
  {
    "image": "images/image_000000.jpg",
    "video": "videos/video_000000.mp4",
    "caption": "A beautiful sunset over the ocean with gentle waves",
    "duration": 5.2,
    "fps": 30.0,
    "resolution": "1920x1080"
  }
]
```

### 3. Configure Training

Edit `config/wan2_1_config.yaml` to match your setup:

```yaml
dataset:
  train_data_dir: "./data/train"
  validation_data_dir: "./data/validation"  # Optional

training:
  output_dir: "./outputs/my-finetuned-model"
  train_batch_size: 1
  gradient_accumulation_steps: 8
  learning_rate: 1e-5
  max_train_steps: 5000

model:
  use_lora: true
  lora_rank: 64
  lora_alpha: 64
```

### 4. Start Training

Choose your model and method:

#### 🎬 Wan2.1 Video Training
```bash
# Standard training (fixed size with cropping)
./scripts/train_lora.sh qlora  # QLoRA (12-24GB VRAM)
./scripts/train_lora.sh lora   # LoRA (24GB+ VRAM)
./scripts/train_lora.sh dora   # DoRA (24GB+ VRAM)

# Variable-size training (no cropping, preserves aspect ratios)
./scripts/train_variable_size.sh wan2.1

# Direct Python commands
python src/train_wan2_1.py --config config/qlora_config.yaml
python src/train_variable_size.py --model_type wan2.1 --config config/wan2_1_variable_size_config.yaml
```

#### 🎨 FLUX Text-to-Image Training
```bash
# Standard training (fixed size with cropping)
./scripts/train_flux.sh lora   # LoRA (16-24GB VRAM)
./scripts/train_flux.sh qlora  # QLoRA (12-16GB VRAM)

# Variable-size training (no cropping, preserves aspect ratios)
./scripts/train_variable_size.sh flux

# Direct Python commands
python src/train_flux.py --config config/flux_lora_config.yaml
python src/train_variable_size.py --model_type flux --config config/flux_variable_size_config.yaml
```

### 5. Generate Content

#### 🎬 Generate Videos (Wan2.1)
```bash
# Using the inference script
./scripts/inference.sh \
    --image_path examples/input.jpg \
    --prompt "A serene landscape with flowing water" \
    --lora_path ./outputs/my-finetuned-model/lora \
    --output_path generated_video.mp4

# Or directly with Python
python src/inference.py \
    --base_model_path Wan-AI/Wan2.1-I2V-14B-720P-Diffusers \
    --lora_path ./outputs/my-finetuned-model/lora \
    --image_path examples/input.jpg \
    --prompt "A serene landscape with flowing water" \
    --output_path generated_video.mp4
```

#### 🎨 Generate Images (FLUX)
```bash
# Using the inference script
./scripts/inference_flux.sh \
    --prompt "A beautiful landscape painting in impressionist style" \
    --lora_path ./outputs/flux-lora-finetuned/lora \
    --output_path generated_image.png

# Or directly with Python
python src/inference_flux.py \
    --base_model_path black-forest-labs/FLUX.1-dev \
    --lora_path ./outputs/flux-lora-finetuned/lora \
    --prompt "A beautiful landscape painting in impressionist style" \
    --output_path generated_image.png
```

## Dataset Structure

Your dataset should follow this structure:

```
data/
├── train/
│   ├── metadata.json
│   ├── images/
│   │   ├── image_000000.jpg
│   │   ├── image_000001.jpg
│   │   └── ...
│   └── videos/
│       ├── video_000000.mp4
│       ├── video_000001.mp4
│       └── ...
└── validation/  # Optional
    ├── metadata.json
    ├── images/
    └── videos/
```

## Configuration Options

### Model Configuration
- `use_lora`: Enable LoRA finetuning (recommended)
- `lora_rank`: LoRA rank (higher = more parameters, better quality)
- `lora_alpha`: LoRA alpha (scaling factor)
- `lora_target_modules`: Which modules to apply LoRA to

### Training Configuration
- `train_batch_size`: Batch size per GPU
- `gradient_accumulation_steps`: Accumulate gradients over multiple steps
- `learning_rate`: Learning rate for optimizer
- `max_train_steps`: Maximum training steps
- `mixed_precision`: Use "bf16" or "fp16" for memory efficiency

### Memory Optimization
- `gradient_checkpointing`: Trade compute for memory
- `use_8bit_adam`: Use 8-bit Adam optimizer
- `enable_xformers_memory_efficient_attention`: Use xFormers for efficiency

## Memory Requirements

### Minimum Requirements (with LoRA + optimizations)
- **GPU Memory**: 24GB (RTX 4090, A6000)
- **System RAM**: 32GB
- **Storage**: 100GB+ for model cache and outputs

### Recommended Setup
- **GPU Memory**: 40GB+ (A100, H100)
- **System RAM**: 64GB+
- **Storage**: 500GB+ SSD

### Memory Optimization Tips

1. **Use LoRA**: Reduces trainable parameters by 99%
2. **Enable gradient checkpointing**: Trades compute for memory
3. **Use mixed precision**: "bf16" or "fp16"
4. **Reduce batch size**: Use gradient accumulation instead
5. **Use 8-bit optimizers**: `use_8bit_adam: true`

## Monitoring and Logging

### Weights & Biases Integration

1. **Install wandb**:
```bash
pip install wandb
wandb login
```

2. **Configure in config file**:
```yaml
training:
  report_to: "wandb"

wandb:
  project_name: "wan2.1-finetuning"
  run_name: "my-experiment"
  tags: ["wan2.1", "custom-dataset"]
```

### Local Logging

Logs and checkpoints are saved to:
- `./logs/`: Training logs
- `./outputs/`: Model checkpoints and final weights
- `./outputs/validation/`: Validation videos

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**:
   - Reduce `train_batch_size`
   - Increase `gradient_accumulation_steps`
   - Enable `gradient_checkpointing`
   - Use `mixed_precision: "bf16"`

2. **Slow Training**:
   - Enable `enable_xformers_memory_efficient_attention`
   - Use `pin_memory: true`
   - Increase `dataloader_num_workers`

3. **Poor Quality Results**:
   - Increase `lora_rank` (64 → 128)
   - Increase training steps
   - Improve dataset quality and captions
   - Adjust learning rate

4. **Model Loading Errors**:
   - Ensure you have the latest `diffusers` version
   - Check internet connection for model downloads
   - Verify model cache directory permissions

### Performance Tips

1. **Dataset Quality**:
   - Use high-quality videos (1080p+)
   - Ensure good image-video correspondence
   - Write descriptive, accurate captions
   - Filter out low-quality or corrupted videos

2. **Training Strategy**:
   - Start with a small dataset to test setup
   - Use validation set to monitor overfitting
   - Save checkpoints frequently
   - Monitor loss curves in wandb

## Advanced Usage

### Multi-GPU Training

```bash
# Using accelerate
accelerate config  # Configure multi-GPU setup
accelerate launch src/train_wan2_1.py --config config/wan2_1_config.yaml
```

### Custom Model Components

You can specify custom paths for model components:

```yaml
model:
  transformer_path: "./custom_transformer"
  vae_path: "./custom_vae"
  text_encoder_path: "./custom_text_encoder"
```

### Resuming Training

```bash
python src/train_wan2_1.py \
    --config config/wan2_1_config.yaml \
    --resume_from_checkpoint ./outputs/checkpoint-1000
```

## License

This project is licensed under the Apache 2.0 License. The Wan2.1 model is also licensed under Apache 2.0.

## Citation

If you use this code in your research, please cite:

```bibtex
@article{wan2.1,
    title   = {Wan: Open and Advanced Large-Scale Video Generative Models},
    author  = {Wan Team},
    journal = {},
    year    = {2025}
}
```

## Contributing

Contributions are welcome! Please feel free to submit issues and pull requests.

## Support

For questions and support:
- Open an issue on GitHub
- Check the [Wan2.1 model page](https://huggingface.co/Wan-AI/Wan2.1-I2V-14B-720P-Diffusers)
- Join the [Discord community](https://discord.gg/p5XbdQV7)
