#!/usr/bin/env python3
"""
Complete Training Example for Wan2.1 LoRA/QLoRA/DoRA Finetuning
This script demonstrates the complete workflow from dataset preparation to inference.
"""

import os
import sys
import subprocess
import json
from pathlib import Path


def run_command(cmd, description=""):
    """Run a command and handle errors"""
    print(f"\n{'='*60}")
    print(f"🔄 {description}")
    print(f"Command: {cmd}")
    print('='*60)
    
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ Success!")
        if result.stdout:
            print(result.stdout)
    else:
        print("❌ Error!")
        if result.stderr:
            print(result.stderr)
        return False
    
    return True


def create_example_dataset():
    """Create a minimal example dataset for testing"""
    print("\n📁 Creating example dataset...")
    
    # Create directory structure
    data_dir = Path("./example_data")
    (data_dir / "images").mkdir(parents=True, exist_ok=True)
    (data_dir / "videos").mkdir(parents=True, exist_ok=True)
    
    # Create example metadata
    metadata = [
        {
            "image": "images/example_001.jpg",
            "video": "videos/example_001.mp4",
            "caption": "A beautiful landscape with flowing water and gentle movement",
            "duration": 5.0,
            "fps": 16.0,
            "resolution": "1280x720"
        },
        {
            "image": "images/example_002.jpg", 
            "video": "videos/example_002.mp4",
            "caption": "A serene forest scene with swaying trees and dappled sunlight",
            "duration": 4.5,
            "fps": 16.0,
            "resolution": "1280x720"
        }
    ]
    
    metadata_path = data_dir / "metadata.json"
    with open(metadata_path, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print(f"✅ Created example dataset structure at {data_dir}")
    print(f"📝 Edit {metadata_path} and add your actual image/video files")
    print(f"📁 Place images in {data_dir / 'images'}")
    print(f"🎥 Place videos in {data_dir / 'videos'}")
    
    return str(data_dir)


def demonstrate_hardware_analysis():
    """Demonstrate hardware analysis"""
    print("\n🔍 Step 1: Hardware Analysis")
    print("This will analyze your hardware and recommend the best LoRA method.")
    
    if not run_command("python examples/memory_optimization_guide.py", "Analyzing hardware"):
        print("⚠️  Hardware analysis failed, but you can continue manually")


def demonstrate_dataset_preparation():
    """Demonstrate dataset preparation"""
    print("\n📁 Step 2: Dataset Preparation")
    
    # Create example dataset
    data_dir = create_example_dataset()
    
    print("\nFor real datasets, use these commands:")
    print("# From videos (extract first frames):")
    print("python scripts/prepare_dataset.py --mode videos --input_dir /path/to/videos --output_dir ./data/train")
    print("\n# From image-video pairs:")
    print("python scripts/prepare_dataset.py --mode pairs --input_dir /path/to/images --videos_dir /path/to/videos --output_dir ./data/train")
    
    return data_dir


def demonstrate_configuration():
    """Demonstrate configuration options"""
    print("\n⚙️  Step 3: Configuration")
    print("Available configuration files:")
    
    configs = [
        ("config/qlora_config.yaml", "QLoRA (4-bit quantization)", "Best for 12-24GB GPUs"),
        ("config/wan2_1_config.yaml", "Standard LoRA", "Good for 24GB+ GPUs"),
        ("config/dora_config.yaml", "DoRA (Weight-Decomposed)", "Best quality for 24GB+ GPUs")
    ]
    
    for config_file, name, description in configs:
        if Path(config_file).exists():
            print(f"✅ {config_file} - {name}")
            print(f"   {description}")
        else:
            print(f"❌ {config_file} - Missing")
    
    print("\nKey configuration differences:")
    print("- QLoRA: use_4bit: true, lora_rank: 32, resolution: 576")
    print("- LoRA: mixed_precision: 'bf16', lora_rank: 64, resolution: 720")
    print("- DoRA: use_dora: true, lora_rank: 32, resolution: 720")


def demonstrate_training():
    """Demonstrate training commands"""
    print("\n🚀 Step 4: Training")
    print("Choose the training method based on your GPU memory:")
    
    training_commands = [
        ("QLoRA (4-bit)", "./scripts/train_lora.sh qlora", "12-24GB GPU memory"),
        ("Standard LoRA", "./scripts/train_lora.sh lora", "24GB+ GPU memory"),
        ("DoRA", "./scripts/train_lora.sh dora", "24GB+ GPU memory, best quality"),
    ]
    
    for method, command, requirement in training_commands:
        print(f"\n{method} ({requirement}):")
        print(f"  {command}")
    
    print("\nDirect Python commands:")
    print("  python src/train_wan2_1.py --config config/qlora_config.yaml")
    print("  python src/train_wan2_1.py --config config/wan2_1_config.yaml")
    print("  python src/train_wan2_1.py --config config/dora_config.yaml")
    
    print("\n⚠️  Note: Training requires actual dataset with images and videos")
    print("This example only shows the commands - replace example_data with your real dataset")


def demonstrate_inference():
    """Demonstrate inference"""
    print("\n🎬 Step 5: Inference")
    print("After training, generate videos with your finetuned model:")
    
    inference_commands = [
        "# Using the inference script:",
        "./scripts/inference.sh \\",
        "    --image_path examples/input.jpg \\",
        "    --prompt 'A beautiful landscape with flowing water' \\",
        "    --lora_path ./outputs/wan2.1-qlora-finetuned/lora \\",
        "    --output_path generated_video.mp4",
        "",
        "# Using Python directly:",
        "python src/inference.py \\",
        "    --base_model_path Wan-AI/Wan2.1-I2V-14B-720P-Diffusers \\",
        "    --lora_path ./outputs/wan2.1-qlora-finetuned/lora \\",
        "    --image_path examples/input.jpg \\",
        "    --prompt 'A beautiful landscape with flowing water' \\",
        "    --output_path generated_video.mp4"
    ]
    
    for cmd in inference_commands:
        print(cmd)


def demonstrate_monitoring():
    """Demonstrate monitoring and validation"""
    print("\n📊 Step 6: Monitoring & Validation")
    print("Monitor your training progress:")
    
    monitoring_tips = [
        "1. Weights & Biases integration:",
        "   - Set report_to: 'wandb' in config",
        "   - Run: wandb login",
        "   - View training curves at wandb.ai",
        "",
        "2. Local monitoring:",
        "   - Check logs in ./logs/",
        "   - Validation videos in ./outputs/validation/",
        "   - GPU usage: watch -n 1 nvidia-smi",
        "",
        "3. Checkpoints:",
        "   - Saved every 500 steps by default",
        "   - Resume with: --resume_from_checkpoint ./outputs/checkpoint-1000",
        "",
        "4. Memory monitoring:",
        "   - Use: nvidia-smi",
        "   - Reduce batch size if OOM",
        "   - Increase gradient accumulation"
    ]
    
    for tip in monitoring_tips:
        print(tip)


def show_troubleshooting():
    """Show troubleshooting guide"""
    print("\n🔧 Troubleshooting")
    
    issues = [
        ("CUDA Out of Memory", [
            "Switch to QLoRA: ./scripts/train_lora.sh qlora",
            "Reduce batch_size to 1",
            "Increase gradient_accumulation_steps",
            "Reduce resolution or num_frames",
            "Enable gradient_checkpointing"
        ]),
        ("Poor Quality Results", [
            "Increase LoRA rank (32 → 64 → 128)",
            "Try DoRA: ./scripts/train_lora.sh dora",
            "Improve dataset quality and captions",
            "Increase training steps",
            "Adjust learning rate"
        ]),
        ("Training Too Slow", [
            "Enable xformers attention",
            "Use pin_memory: true",
            "Increase dataloader_num_workers",
            "Use SSD storage for dataset"
        ])
    ]
    
    for issue, solutions in issues:
        print(f"\n❌ {issue}:")
        for i, solution in enumerate(solutions, 1):
            print(f"   {i}. {solution}")


def main():
    """Main demonstration function"""
    print("Wan2.1 LoRA/QLoRA/DoRA Complete Training Example")
    print("=" * 60)
    print("This script demonstrates the complete workflow for finetuning Wan2.1")
    print("with LoRA, QLoRA (4-bit), and DoRA techniques.")
    print("")
    
    # Check if we're in the right directory
    if not Path("config/wan2_1_config.yaml").exists():
        print("⚠️  Warning: Run this script from the project root directory")
        print("   Expected files: config/wan2_1_config.yaml")
        print("")
    
    # Run demonstrations
    demonstrate_hardware_analysis()
    demonstrate_dataset_preparation()
    demonstrate_configuration()
    demonstrate_training()
    demonstrate_inference()
    demonstrate_monitoring()
    show_troubleshooting()
    
    print("\n" + "="*60)
    print("🎉 Complete Workflow Summary:")
    print("1. Analyze hardware: python examples/memory_optimization_guide.py")
    print("2. Prepare dataset: python scripts/prepare_dataset.py")
    print("3. Choose method: ./scripts/train_lora.sh [lora|qlora|dora]")
    print("4. Monitor training: wandb or local logs")
    print("5. Generate videos: ./scripts/inference.sh")
    print("")
    print("📖 For detailed documentation, see README.md")
    print("🐛 For issues, check the troubleshooting section above")


if __name__ == "__main__":
    main()
