#!/usr/bin/env python3
"""
Example usage of Wan2.1 finetuning pipeline
"""

import os
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from dataset import ImageVideoDataset, create_metadata_template
from inference import load_finetuned_pipeline, generate_video


def example_dataset_creation():
    """Example of creating a dataset from videos"""
    print("=== Dataset Creation Example ===")
    
    # Create example data structure
    data_dir = Path("./example_data")
    data_dir.mkdir(exist_ok=True)
    (data_dir / "videos").mkdir(exist_ok=True)
    
    print(f"1. Place your video files in: {data_dir / 'videos'}")
    print("2. Run the dataset preparation script:")
    print(f"   python scripts/prepare_dataset.py --mode videos --input_dir {data_dir / 'videos'} --output_dir ./data/train")
    print("3. Edit the generated metadata.json to add proper captions")
    

def example_dataset_loading():
    """Example of loading and testing the dataset"""
    print("\n=== Dataset Loading Example ===")
    
    # Check if dataset exists
    data_dir = "./data/train"
    if not os.path.exists(data_dir):
        print(f"Dataset not found at {data_dir}")
        print("Please create a dataset first using the preparation script")
        return
    
    try:
        # Create dataset
        dataset = ImageVideoDataset(
            data_dir=data_dir,
            width=1280,
            height=720,
            num_frames=81,
            max_sequence_length=256,
        )
        
        print(f"Dataset loaded successfully!")
        print(f"Number of samples: {len(dataset)}")
        
        if len(dataset) > 0:
            # Load first sample
            sample = dataset[0]
            print(f"Sample 0:")
            print(f"  Image CLIP shape: {sample['image_clip'].shape}")
            print(f"  Image VAE shape: {sample['image_vae'].shape}")
            print(f"  Video shape: {sample['video'].shape}")
            print(f"  Caption: {sample['caption']}")
            print(f"  Image path: {sample['image_path']}")
            print(f"  Video path: {sample['video_path']}")
            
    except Exception as e:
        print(f"Error loading dataset: {e}")


def example_training_config():
    """Example of training configuration"""
    print("\n=== Training Configuration Example ===")
    
    config_example = """
# Example training command:
python src/train_wan2_1.py --config config/wan2_1_config.yaml

# Key configuration options:
model:
  use_lora: true          # Enable LoRA for efficient finetuning
  lora_rank: 64          # Higher rank = more parameters
  lora_alpha: 64         # Scaling factor

training:
  train_batch_size: 1              # Batch size per GPU
  gradient_accumulation_steps: 8   # Effective batch size = 1 * 8 = 8
  learning_rate: 1e-5             # Learning rate
  max_train_steps: 5000           # Total training steps
  mixed_precision: "bf16"         # Use bfloat16 for memory efficiency
  
dataset:
  train_data_dir: "./data/train"
  max_sequence_length: 256
"""
    
    print(config_example)


def example_inference():
    """Example of running inference with finetuned model"""
    print("\n=== Inference Example ===")
    
    # Check if we have a finetuned model
    lora_path = "./outputs/wan2.1-finetuned/lora"
    
    if os.path.exists(lora_path):
        print("Found finetuned LoRA weights!")
        print(f"LoRA path: {lora_path}")
    else:
        print("No finetuned model found. Using base model.")
        lora_path = None
    
    inference_example = f"""
# Example inference commands:

# Using the inference script:
./scripts/inference.sh \\
    --image_path examples/input.jpg \\
    --prompt "A beautiful landscape with flowing water" \\
    --lora_path {lora_path or "path/to/your/lora"} \\
    --output_path generated_video.mp4

# Using Python directly:
python src/inference.py \\
    --base_model_path Wan-AI/Wan2.1-I2V-14B-720P-Diffusers \\
    --lora_path {lora_path or "path/to/your/lora"} \\
    --image_path examples/input.jpg \\
    --prompt "A beautiful landscape with flowing water" \\
    --output_path generated_video.mp4 \\
    --guidance_scale 5.0 \\
    --num_inference_steps 50
"""
    
    print(inference_example)


def example_memory_optimization():
    """Example of memory optimization techniques"""
    print("\n=== Memory Optimization Example ===")
    
    optimization_tips = """
Memory Optimization Techniques:

1. LoRA Finetuning (Recommended):
   model:
     use_lora: true
     lora_rank: 64        # Start with 64, increase if needed

2. Gradient Checkpointing:
   training:
     gradient_checkpointing: true

3. Mixed Precision:
   training:
     mixed_precision: "bf16"  # or "fp16"

4. Batch Size Optimization:
   training:
     train_batch_size: 1
     gradient_accumulation_steps: 8  # Effective batch size = 8

5. 8-bit Optimizer:
   training:
     use_8bit_adam: true

6. Memory Efficient Attention:
   training:
     enable_xformers_memory_efficient_attention: true

Estimated Memory Usage:
- Base model (no LoRA): ~28GB VRAM
- With LoRA + optimizations: ~16-20GB VRAM
- Minimum recommended: 24GB VRAM (RTX 4090)
"""
    
    print(optimization_tips)


def example_dataset_structure():
    """Show example dataset structure"""
    print("\n=== Dataset Structure Example ===")
    
    structure = """
Expected dataset structure:

data/
├── train/
│   ├── metadata.json
│   ├── images/
│   │   ├── image_000000.jpg
│   │   ├── image_000001.jpg
│   │   └── ...
│   └── videos/
│       ├── video_000000.mp4
│       ├── video_000001.mp4
│       └── ...
└── validation/  # Optional
    ├── metadata.json
    ├── images/
    └── videos/

metadata.json format:
[
  {
    "image": "images/image_000000.jpg",
    "video": "videos/video_000000.mp4",
    "caption": "A beautiful sunset over the ocean with gentle waves",
    "duration": 5.2,
    "fps": 30.0,
    "resolution": "1920x1080"
  },
  ...
]
"""
    
    print(structure)


def main():
    """Run all examples"""
    print("Wan2.1 Image-to-Video Finetuning - Usage Examples")
    print("=" * 60)
    
    example_dataset_structure()
    example_dataset_creation()
    example_dataset_loading()
    example_training_config()
    example_inference()
    example_memory_optimization()
    
    print("\n" + "=" * 60)
    print("For more information, see README.md")
    print("For support, visit: https://huggingface.co/Wan-AI/Wan2.1-I2V-14B-720P-Diffusers")


if __name__ == "__main__":
    main()
