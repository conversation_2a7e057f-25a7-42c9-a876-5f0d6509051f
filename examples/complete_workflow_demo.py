#!/usr/bin/env python3
"""
Complete Workflow Demo for Both Wan2.1 and FLUX.1-dev Finetuning
This script demonstrates the complete workflow for both models.
"""

import os
import sys
import subprocess
import json
from pathlib import Path


def print_header(title):
    """Print a formatted header"""
    print(f"\n{'='*80}")
    print(f"🚀 {title}")
    print('='*80)


def print_section(title):
    """Print a formatted section"""
    print(f"\n{'─'*60}")
    print(f"📋 {title}")
    print('─'*60)


def run_command(cmd, description=""):
    """Run a command and handle errors"""
    if description:
        print(f"\n🔄 {description}")
        print(f"Command: {cmd}")
    
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ Success!")
        if result.stdout:
            print(result.stdout)
    else:
        print("❌ Error!")
        if result.stderr:
            print(result.stderr)
        return False
    
    return True


def demo_hardware_analysis():
    """Demonstrate hardware analysis"""
    print_header("Hardware Analysis & Recommendations")
    
    print("This will analyze your hardware and recommend the best training methods.")
    print("Run this first to understand your system capabilities:")
    print("")
    print("python examples/memory_optimization_guide.py")
    print("")
    print("Expected output:")
    print("- GPU memory analysis")
    print("- Recommended LoRA methods")
    print("- Memory optimization tips")


def demo_wan2_1_workflow():
    """Demonstrate Wan2.1 video generation workflow"""
    print_header("🎬 Wan2.1 Image-to-Video Workflow")
    
    print_section("1. Dataset Preparation")
    print("Prepare your video dataset:")
    print("")
    print("# Option A: From videos (extract first frames)")
    print("python scripts/prepare_dataset.py \\")
    print("    --mode videos \\")
    print("    --input_dir /path/to/your/videos \\")
    print("    --output_dir ./data/wan2_1_train \\")
    print("    --min_duration 2.0")
    print("")
    print("# Option B: From image-video pairs")
    print("python scripts/prepare_dataset.py \\")
    print("    --mode pairs \\")
    print("    --input_dir /path/to/images \\")
    print("    --videos_dir /path/to/videos \\")
    print("    --output_dir ./data/wan2_1_train")
    
    print_section("2. Edit Captions")
    print("Edit ./data/wan2_1_train/metadata.json to add proper captions:")
    print("")
    example_metadata = [
        {
            "image": "images/image_000000.jpg",
            "video": "videos/video_000000.mp4",
            "caption": "A serene mountain lake with gentle ripples and morning mist",
            "duration": 5.2,
            "fps": 30.0,
            "resolution": "1920x1080"
        }
    ]
    print(json.dumps(example_metadata, indent=2))
    
    print_section("3. Training Commands")
    print("Choose based on your GPU memory:")
    print("")
    print("# QLoRA (12-24GB GPU) - Most memory efficient")
    print("./scripts/train_lora.sh qlora")
    print("")
    print("# Standard LoRA (24GB+ GPU) - Good balance")
    print("./scripts/train_lora.sh lora")
    print("")
    print("# DoRA (24GB+ GPU) - Best quality")
    print("./scripts/train_lora.sh dora")
    
    print_section("4. Video Generation")
    print("Generate videos with your trained model:")
    print("")
    print("./scripts/inference.sh \\")
    print("    --image_path input.jpg \\")
    print("    --prompt 'A beautiful landscape with flowing water' \\")
    print("    --lora_path ./outputs/wan2.1-qlora-finetuned/lora \\")
    print("    --output_path generated_video.mp4")


def demo_flux_workflow():
    """Demonstrate FLUX text-to-image workflow"""
    print_header("🎨 FLUX.1-dev Text-to-Image Workflow")
    
    print_section("1. Dataset Preparation")
    print("Choose your training type:")
    print("")
    print("# Style LoRA (recommended for beginners)")
    print("python scripts/prepare_flux_dataset.py \\")
    print("    --mode style \\")
    print("    --input_dir /path/to/style/images \\")
    print("    --output_dir ./data/flux_style \\")
    print("    --style_name 'MYSTYLE' \\")
    print("    --resize_images")
    print("")
    print("# Dreambooth (for specific subjects)")
    print("python scripts/prepare_flux_dataset.py \\")
    print("    --mode dreambooth \\")
    print("    --input_dir /path/to/subject/images \\")
    print("    --output_dir ./data/flux_dreambooth \\")
    print("    --instance_prompt 'a photo of sks person' \\")
    print("    --resize_images")
    print("")
    print("# Text-to-Image (general purpose)")
    print("python scripts/prepare_flux_dataset.py \\")
    print("    --mode text2image \\")
    print("    --input_dir /path/to/images \\")
    print("    --output_dir ./data/flux_text2image \\")
    print("    --resize_images")
    
    print_section("2. Training Commands")
    print("FLUX training options:")
    print("")
    print("# LoRA (16-24GB GPU)")
    print("./scripts/train_flux.sh lora")
    print("")
    print("# QLoRA (12-16GB GPU) - Memory efficient")
    print("./scripts/train_flux.sh qlora")
    
    print_section("3. Image Generation")
    print("Generate images with your trained model:")
    print("")
    print("# Basic generation")
    print("./scripts/inference_flux.sh \\")
    print("    --prompt 'A beautiful landscape painting'")
    print("")
    print("# With style LoRA")
    print("./scripts/inference_flux.sh \\")
    print("    --prompt 'A portrait in MYSTYLE style' \\")
    print("    --lora_path ./outputs/flux-lora-finetuned/lora")
    print("")
    print("# Batch generation")
    print("echo 'A portrait in MYSTYLE style' > prompts.txt")
    print("echo 'A landscape in MYSTYLE style' >> prompts.txt")
    print("./scripts/inference_flux.sh \\")
    print("    --batch_prompts prompts.txt \\")
    print("    --lora_path ./outputs/flux-lora-finetuned/lora \\")
    print("    --output_dir ./batch_outputs")


def demo_memory_optimization():
    """Demonstrate memory optimization strategies"""
    print_header("💾 Memory Optimization Strategies")
    
    print_section("GPU Memory Requirements")
    memory_table = [
        ["Model", "Method", "Memory", "Quality", "Speed"],
        ["Wan2.1", "QLoRA", "14-18GB", "Good", "Moderate"],
        ["Wan2.1", "LoRA", "20-24GB", "Excellent", "Fast"],
        ["Wan2.1", "DoRA", "20-24GB", "Superior", "Moderate"],
        ["FLUX", "QLoRA", "12-16GB", "Good", "Moderate"],
        ["FLUX", "LoRA", "16-20GB", "Excellent", "Fast"],
    ]
    
    for row in memory_table:
        print(f"  {row[0]:<8} {row[1]:<6} {row[2]:<10} {row[3]:<10} {row[4]}")
    
    print_section("Optimization Techniques")
    optimizations = [
        ("QLoRA 4-bit", "Reduces memory by 40-50%", "use_4bit: true"),
        ("Gradient Checkpointing", "Trade compute for memory", "gradient_checkpointing: true"),
        ("Mixed Precision", "Use bf16/fp16", "mixed_precision: 'bf16'"),
        ("8-bit Adam", "Reduce optimizer memory", "use_8bit_adam: true"),
        ("Lower Resolution", "Reduce VRAM usage", "resolution: 768"),
        ("Gradient Accumulation", "Simulate larger batches", "gradient_accumulation_steps: 8"),
    ]
    
    for name, desc, setting in optimizations:
        print(f"  {name:<20} {desc:<25} {setting}")


def demo_troubleshooting():
    """Demonstrate troubleshooting guide"""
    print_header("🔧 Troubleshooting Guide")
    
    issues = [
        ("CUDA Out of Memory", [
            "Switch to QLoRA: ./scripts/train_[model].sh qlora",
            "Reduce batch_size to 1",
            "Increase gradient_accumulation_steps",
            "Reduce resolution (720p → 576p → 480p)",
            "Enable gradient_checkpointing: true"
        ]),
        ("Poor Quality Results", [
            "Increase LoRA rank (16 → 32 → 64)",
            "Try DoRA for Wan2.1: ./scripts/train_lora.sh dora",
            "Improve dataset quality and captions",
            "Increase training steps",
            "Adjust learning rate (1e-5 to 5e-4)"
        ]),
        ("Training Too Slow", [
            "Enable xformers: enable_xformers_memory_efficient_attention: true",
            "Use pin_memory: true",
            "Increase dataloader_num_workers (2-8)",
            "Use SSD storage for dataset",
            "Check GPU utilization with nvidia-smi"
        ]),
        ("Model Loading Errors", [
            "Update packages: pip install -U diffusers transformers",
            "Clear cache: rm -rf ~/.cache/huggingface/",
            "Check internet connection",
            "Verify model path in config"
        ])
    ]
    
    for issue, solutions in issues:
        print_section(f"❌ {issue}")
        for i, solution in enumerate(solutions, 1):
            print(f"  {i}. {solution}")


def demo_advanced_techniques():
    """Demonstrate advanced techniques"""
    print_header("🎯 Advanced Techniques")
    
    print_section("Multi-GPU Training")
    print("# Configure accelerate for multi-GPU")
    print("accelerate config")
    print("")
    print("# Launch multi-GPU training")
    print("accelerate launch src/train_wan2_1.py --config config/wan2_1_config.yaml")
    print("accelerate launch src/train_flux.py --config config/flux_lora_config.yaml")
    
    print_section("Experiment Tracking")
    print("# Setup Weights & Biases")
    print("pip install wandb")
    print("wandb login")
    print("")
    print("# Enable in config")
    print("training:")
    print("  report_to: 'wandb'")
    print("")
    print("wandb:")
    print("  project_name: 'my-diffusion-experiments'")
    print("  run_name: 'flux-style-lora-v1'")
    
    print_section("Progressive Training")
    print("# Start with lower resolution, increase gradually")
    print("# 1. Train at 512x512 for 1000 steps")
    print("# 2. Continue at 768x768 for 1000 steps")
    print("# 3. Finish at 1024x1024 for 2000 steps")
    
    print_section("Ensemble Methods")
    print("# Combine multiple LoRAs")
    print("pipe.load_lora_weights('style_lora', adapter_name='style')")
    print("pipe.load_lora_weights('concept_lora', adapter_name='concept')")
    print("pipe.set_adapters(['style', 'concept'], adapter_weights=[0.8, 0.6])")


def main():
    """Main demonstration function"""
    print("🎬🎨 Advanced Diffusion Model Finetuning Suite")
    print("Complete Workflow Demonstration")
    print("=" * 80)
    print("")
    print("This demo covers both Wan2.1 (video) and FLUX.1-dev (image) finetuning")
    print("with LoRA, QLoRA, and DoRA techniques.")
    print("")
    
    # Check if we're in the right directory
    if not Path("config/wan2_1_config.yaml").exists():
        print("⚠️  Warning: Run this script from the project root directory")
        print("   Expected files: config/wan2_1_config.yaml, config/flux_lora_config.yaml")
        print("")
    
    # Run demonstrations
    demo_hardware_analysis()
    demo_wan2_1_workflow()
    demo_flux_workflow()
    demo_memory_optimization()
    demo_troubleshooting()
    demo_advanced_techniques()
    
    print_header("🎉 Summary & Next Steps")
    print("Complete workflow for both models:")
    print("")
    print("1. 🔍 Analyze hardware: python examples/memory_optimization_guide.py")
    print("2. 📁 Prepare datasets: python scripts/prepare_[model]_dataset.py")
    print("3. 🚀 Train models: ./scripts/train_[model].sh [method]")
    print("4. 🎬🎨 Generate content: ./scripts/inference_[model].sh")
    print("")
    print("📖 Detailed guides:")
    print("   - README.md: General overview and setup")
    print("   - FLUX_TRAINING_GUIDE.md: Comprehensive FLUX guide")
    print("   - examples/: Additional examples and utilities")
    print("")
    print("🐛 For issues:")
    print("   - Check troubleshooting section above")
    print("   - Review model-specific documentation")
    print("   - Monitor GPU usage with nvidia-smi")


if __name__ == "__main__":
    main()
