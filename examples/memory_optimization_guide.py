#!/usr/bin/env python3
"""
Memory Optimization Guide for Wan2.1 LoRA/QLoRA/DoRA Finetuning
"""

import torch
import psutil
import subprocess
import sys
from pathlib import Path


def get_gpu_memory():
    """Get GPU memory information"""
    try:
        result = subprocess.run(['nvidia-smi', '--query-gpu=memory.total,memory.free', '--format=csv,noheader,nounits'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            gpu_info = []
            for i, line in enumerate(lines):
                total, free = map(int, line.split(', '))
                gpu_info.append({
                    'gpu_id': i,
                    'total_mb': total,
                    'free_mb': free,
                    'used_mb': total - free
                })
            return gpu_info
        return None
    except:
        return None


def get_system_memory():
    """Get system RAM information"""
    memory = psutil.virtual_memory()
    return {
        'total_gb': memory.total / (1024**3),
        'available_gb': memory.available / (1024**3),
        'used_gb': memory.used / (1024**3),
        'percent': memory.percent
    }


def recommend_configuration():
    """Recommend optimal configuration based on available hardware"""
    print("🔍 Analyzing your hardware for optimal Wan2.1 finetuning configuration...\n")
    
    # Get hardware info
    gpu_info = get_gpu_memory()
    system_memory = get_system_memory()
    
    print("💻 System Information:")
    print(f"   RAM: {system_memory['available_gb']:.1f}GB available / {system_memory['total_gb']:.1f}GB total")
    
    if gpu_info:
        for gpu in gpu_info:
            print(f"   GPU {gpu['gpu_id']}: {gpu['free_mb']/1024:.1f}GB free / {gpu['total_mb']/1024:.1f}GB total")
    else:
        print("   GPU: Not detected or nvidia-smi not available")
        
    print("\n" + "="*60)
    
    # Determine best configuration
    if not gpu_info or not gpu_info[0]['free_mb']:
        print("❌ No GPU detected - CPU training not recommended for Wan2.1")
        return
    
    max_gpu_memory = max(gpu['free_mb'] for gpu in gpu_info)
    
    print("🎯 Recommended Configuration:\n")
    
    if max_gpu_memory < 8000:  # < 8GB
        print("⚠️  Very Limited GPU Memory (<8GB)")
        print("   Status: Training not recommended")
        print("   Reason: Wan2.1 14B model requires significant memory even with optimizations")
        print("   Suggestions:")
        print("   - Consider using a smaller model")
        print("   - Use cloud GPU services (Colab Pro, AWS, etc.)")
        print("   - Wait for smaller Wan2.1 variants")
        
    elif max_gpu_memory < 12000:  # 8-12GB
        print("⚠️  Limited GPU Memory (8-12GB)")
        print("   Recommended: QLoRA with aggressive optimizations")
        print("   Command: ./scripts/train_lora.sh qlora")
        print("   Settings:")
        print("   - 4-bit quantization (NF4)")
        print("   - LoRA rank: 16-32")
        print("   - Batch size: 1")
        print("   - Gradient accumulation: 16-32")
        print("   - Resolution: 576p")
        print("   - Frames: 25-49")
        
    elif max_gpu_memory < 20000:  # 12-20GB
        print("✅ Moderate GPU Memory (12-20GB)")
        print("   Recommended: QLoRA or LoRA with optimizations")
        print("   Commands:")
        print("   - QLoRA (most memory efficient): ./scripts/train_lora.sh qlora")
        print("   - LoRA (better quality): ./scripts/train_lora.sh lora")
        print("   Settings:")
        print("   - QLoRA: 4-bit quantization, rank 32-64")
        print("   - LoRA: bf16, rank 32-64, gradient checkpointing")
        print("   - Batch size: 1")
        print("   - Gradient accumulation: 8-16")
        print("   - Resolution: 720p")
        print("   - Frames: 49-81")
        
    elif max_gpu_memory < 40000:  # 20-40GB
        print("✅ Good GPU Memory (20-40GB)")
        print("   Recommended: LoRA or DoRA")
        print("   Commands:")
        print("   - LoRA (standard): ./scripts/train_lora.sh lora")
        print("   - DoRA (potentially better): ./scripts/train_lora.sh dora")
        print("   - QLoRA (maximum efficiency): ./scripts/train_lora.sh qlora")
        print("   Settings:")
        print("   - LoRA/DoRA: bf16, rank 64-128")
        print("   - Batch size: 1-2")
        print("   - Gradient accumulation: 4-8")
        print("   - Resolution: 720p")
        print("   - Frames: 81")
        
    else:  # >40GB
        print("🚀 Excellent GPU Memory (>40GB)")
        print("   Recommended: Any method with optimal settings")
        print("   Commands:")
        print("   - DoRA (best quality): ./scripts/train_lora.sh dora")
        print("   - LoRA (reliable): ./scripts/train_lora.sh lora")
        print("   - QLoRA (experimental): ./scripts/train_lora.sh qlora")
        print("   Settings:")
        print("   - Rank: 64-256")
        print("   - Batch size: 2-4")
        print("   - Gradient accumulation: 2-4")
        print("   - Resolution: 720p")
        print("   - Frames: 81")
    
    print("\n" + "="*60)
    print("💡 General Optimization Tips:")
    print("   1. Start with QLoRA for initial experiments")
    print("   2. Use gradient checkpointing (enabled by default)")
    print("   3. Enable xFormers attention (enabled by default)")
    print("   4. Monitor GPU memory with: watch -n 1 nvidia-smi")
    print("   5. Reduce resolution/frames if OOM occurs")
    print("   6. Use 8-bit Adam optimizer (enabled in configs)")


def show_config_comparison():
    """Show comparison between different LoRA methods"""
    print("\n" + "="*60)
    print("📊 LoRA Method Comparison:")
    print("="*60)
    
    methods = [
        {
            "name": "Standard LoRA",
            "memory": "High",
            "quality": "Good",
            "speed": "Fast",
            "description": "Original LoRA method, reliable and well-tested"
        },
        {
            "name": "QLoRA (4-bit)",
            "memory": "Very Low",
            "quality": "Good*",
            "speed": "Moderate",
            "description": "4-bit quantization + LoRA, best for limited memory"
        },
        {
            "name": "DoRA",
            "memory": "High",
            "quality": "Better",
            "speed": "Moderate",
            "description": "Weight-decomposed LoRA, often outperforms standard LoRA"
        }
    ]
    
    for method in methods:
        print(f"\n{method['name']}:")
        print(f"   Memory Usage: {method['memory']}")
        print(f"   Quality: {method['quality']}")
        print(f"   Training Speed: {method['speed']}")
        print(f"   Description: {method['description']}")
    
    print("\n* QLoRA quality depends on quantization settings and may require hyperparameter tuning")


def show_troubleshooting():
    """Show common issues and solutions"""
    print("\n" + "="*60)
    print("🔧 Troubleshooting Common Issues:")
    print("="*60)
    
    issues = [
        {
            "issue": "CUDA Out of Memory (OOM)",
            "solutions": [
                "Switch to QLoRA: ./scripts/train_lora.sh qlora",
                "Reduce batch_size to 1",
                "Increase gradient_accumulation_steps",
                "Reduce resolution (720p → 576p → 480p)",
                "Reduce num_frames (81 → 49 → 25)",
                "Enable gradient_checkpointing: true",
                "Use mixed_precision: 'bf16'"
            ]
        },
        {
            "issue": "Training is very slow",
            "solutions": [
                "Enable xformers: enable_xformers_memory_efficient_attention: true",
                "Use pin_memory: true",
                "Increase dataloader_num_workers (2-8)",
                "Use SSD storage for dataset",
                "Check GPU utilization with nvidia-smi"
            ]
        },
        {
            "issue": "Poor quality results",
            "solutions": [
                "Increase LoRA rank (32 → 64 → 128)",
                "Try DoRA: ./scripts/train_lora.sh dora",
                "Improve dataset quality and captions",
                "Increase training steps",
                "Adjust learning rate (try 1e-5 to 5e-5)",
                "Use validation to monitor overfitting"
            ]
        },
        {
            "issue": "Model loading errors",
            "solutions": [
                "Update diffusers: pip install -U diffusers",
                "Update transformers: pip install -U transformers",
                "Clear cache: rm -rf ~/.cache/huggingface/",
                "Check internet connection",
                "Verify model path in config"
            ]
        }
    ]
    
    for issue_info in issues:
        print(f"\n❌ {issue_info['issue']}:")
        for i, solution in enumerate(issue_info['solutions'], 1):
            print(f"   {i}. {solution}")


def main():
    """Main function"""
    print("Wan2.1 LoRA/QLoRA/DoRA Memory Optimization Guide")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("config/wan2_1_config.yaml").exists():
        print("⚠️  Warning: Run this script from the project root directory")
        print("   Expected files: config/wan2_1_config.yaml")
        print("")
    
    recommend_configuration()
    show_config_comparison()
    show_troubleshooting()
    
    print("\n" + "="*60)
    print("🚀 Ready to start training? Use these commands:")
    print("   ./scripts/train_lora.sh lora    # Standard LoRA")
    print("   ./scripts/train_lora.sh qlora   # QLoRA (4-bit)")
    print("   ./scripts/train_lora.sh dora    # DoRA")
    print("\n📖 For more details, see README.md")


if __name__ == "__main__":
    main()
