#!/usr/bin/env python3
"""
Automatic Dataset Preparation Demo
Demonstrates the automatic captioning and dataset preparation capabilities
"""

import os
import sys
import json
import tempfile
import shutil
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import numpy as np


def create_demo_images(output_dir: Path, num_images: int = 20):
    """Create demo images with various styles and content"""
    
    print(f"Creating {num_images} demo images...")
    
    # Image configurations with different styles
    image_configs = [
        # Landscapes
        {"size": (1024, 768), "color": (135, 206, 235), "style": "landscape", "objects": ["mountains", "sky", "clouds"]},
        {"size": (1280, 720), "color": (34, 139, 34), "style": "landscape", "objects": ["forest", "trees", "nature"]},
        {"size": (800, 600), "color": (255, 218, 185), "style": "landscape", "objects": ["desert", "sand", "dunes"]},
        
        # Portraits
        {"size": (600, 800), "color": (255, 182, 193), "style": "portrait", "objects": ["person", "face", "expression"]},
        {"size": (512, 768), "color": (221, 160, 221), "style": "portrait", "objects": ["woman", "smile", "beauty"]},
        {"size": (576, 864), "color": (173, 216, 230), "style": "portrait", "objects": ["man", "professional", "business"]},
        
        # Objects and Still Life
        {"size": (768, 768), "color": (255, 165, 0), "style": "still_life", "objects": ["fruit", "bowl", "table"]},
        {"size": (640, 640), "color": (255, 20, 147), "style": "still_life", "objects": ["flowers", "vase", "decoration"]},
        {"size": (896, 896), "color": (64, 224, 208), "style": "still_life", "objects": ["books", "coffee", "study"]},
        
        # Architecture
        {"size": (1200, 800), "color": (169, 169, 169), "style": "architecture", "objects": ["building", "modern", "glass"]},
        {"size": (960, 640), "color": (139, 69, 19), "style": "architecture", "objects": ["house", "traditional", "brick"]},
        {"size": (1024, 576), "color": (105, 105, 105), "style": "architecture", "objects": ["bridge", "steel", "engineering"]},
        
        # Abstract Art
        {"size": (512, 512), "color": (255, 0, 255), "style": "abstract", "objects": ["geometric", "shapes", "colorful"]},
        {"size": (768, 512), "color": (0, 255, 255), "style": "abstract", "objects": ["pattern", "design", "artistic"]},
        {"size": (1024, 1024), "color": (255, 255, 0), "style": "abstract", "objects": ["composition", "modern", "creative"]},
        
        # Animals
        {"size": (800, 800), "color": (210, 180, 140), "style": "animal", "objects": ["dog", "pet", "friendly"]},
        {"size": (720, 960), "color": (255, 228, 196), "style": "animal", "objects": ["cat", "cute", "domestic"]},
        {"size": (1280, 960), "color": (144, 238, 144), "style": "animal", "objects": ["wildlife", "nature", "wild"]},
        
        # Food
        {"size": (640, 480), "color": (255, 99, 71), "style": "food", "objects": ["meal", "delicious", "restaurant"]},
        {"size": (854, 480), "color": (255, 215, 0), "style": "food", "objects": ["dessert", "sweet", "bakery"]},
    ]
    
    # Ensure we have enough configs
    while len(image_configs) < num_images:
        image_configs.extend(image_configs[:num_images - len(image_configs)])
    
    for i in range(num_images):
        config = image_configs[i % len(image_configs)]
        
        # Create image
        img = Image.new('RGB', config["size"], config["color"])
        draw = ImageDraw.Draw(img)
        
        # Add some visual elements
        width, height = config["size"]
        
        # Add gradient effect
        for y in range(height):
            alpha = y / height
            gradient_color = tuple(int(c * (1 - alpha * 0.3)) for c in config["color"])
            draw.line([(0, y), (width, y)], fill=gradient_color)
        
        # Add geometric shapes based on style
        if config["style"] == "landscape":
            # Add mountain-like shapes
            points = [(0, height), (width//4, height//2), (width//2, height//3), (3*width//4, height//2), (width, height)]
            draw.polygon(points, fill=(100, 100, 100))
            
        elif config["style"] == "portrait":
            # Add oval for face
            oval_size = min(width, height) // 3
            x = (width - oval_size) // 2
            y = (height - oval_size) // 2
            draw.ellipse([x, y, x + oval_size, y + oval_size], fill=(255, 220, 177))
            
        elif config["style"] == "abstract":
            # Add random shapes
            for _ in range(5):
                x1, y1 = np.random.randint(0, width), np.random.randint(0, height)
                x2, y2 = np.random.randint(0, width), np.random.randint(0, height)
                color = tuple(np.random.randint(0, 256, 3))
                draw.rectangle([min(x1,x2), min(y1,y2), max(x1,x2), max(y1,y2)], fill=color)
        
        # Add text overlay with style and objects info
        try:
            font = ImageFont.load_default()
        except:
            font = None
            
        text_lines = [
            f"Style: {config['style']}",
            f"Size: {width}x{height}",
            f"Objects: {', '.join(config['objects'][:2])}"
        ]
        
        # Draw text background
        text_height = 60
        draw.rectangle([10, height - text_height - 10, width - 10, height - 10], fill=(0, 0, 0, 128))
        
        # Draw text
        y_offset = height - text_height
        for line in text_lines:
            draw.text((20, y_offset), line, fill=(255, 255, 255), font=font)
            y_offset += 15
        
        # Save image
        filename = f"demo_image_{i:03d}.jpg"
        img.save(output_dir / filename, quality=95)
        
    print(f"✅ Created {num_images} demo images in {output_dir}")


def demonstrate_auto_captioning():
    """Demonstrate automatic captioning capabilities"""
    
    print("\n" + "="*60)
    print("🎨 AUTOMATIC DATASET PREPARATION DEMO")
    print("="*60)
    
    # Create temporary directory for demo
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        input_dir = temp_path / "input_images"
        output_dir = temp_path / "prepared_dataset"
        
        input_dir.mkdir()
        
        # Create demo images
        create_demo_images(input_dir, num_images=10)
        
        print(f"\n📁 Demo images created in: {input_dir}")
        print(f"📁 Output will be saved to: {output_dir}")
        
        # Show available captioning models
        try:
            from src.auto_caption import get_available_models
            available_models = get_available_models()
            print(f"\n🤖 Available captioning models: {available_models}")
            
            if not available_models:
                print("\n⚠️  No captioning models available!")
                print("To install dependencies:")
                print("pip install transformers torch torchvision")
                print("pip install clip-by-openai  # Optional for CLIP")
                return
                
        except ImportError:
            print("\n⚠️  Auto-captioning modules not available!")
            print("Please ensure the src/ directory is in your Python path")
            return
        
        # Demonstrate dataset preparation
        print(f"\n🚀 Starting automatic dataset preparation...")
        print("This will:")
        print("  1. Analyze image quality and properties")
        print("  2. Generate captions using AI models")
        print("  3. Create organized dataset structure")
        print("  4. Save metadata in FLUX-ready format")
        
        try:
            from src.auto_dataset_prep import AutoDatasetPreparator
            
            # Create preparator with demo settings
            preparator = AutoDatasetPreparator(
                input_dir=str(input_dir),
                output_dir=str(output_dir),
                captioning_model=available_models[0],  # Use first available model
                max_images=10,
                min_resolution=256,  # Lower for demo
                quality_threshold=0.5,  # Lower for demo
                batch_size=2,
                enhance_captions=True
            )
            
            # Run preparation
            stats = preparator.prepare_dataset()
            
            # Show results
            print(f"\n✅ Dataset preparation completed!")
            print(f"📊 Processing statistics:")
            for key, value in stats.items():
                if isinstance(value, (int, float)):
                    print(f"  {key}: {value}")
            
            # Show sample metadata
            metadata_path = output_dir / "metadata.json"
            if metadata_path.exists():
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                    
                print(f"\n📄 Sample metadata entries:")
                for i, item in enumerate(metadata[:3]):
                    print(f"\n  Image {i+1}:")
                    print(f"    File: {item['image']}")
                    print(f"    Size: {item['width']}x{item['height']}")
                    print(f"    Caption: {item['caption']}")
                    print(f"    Quality: {item['quality_score']:.2f}")
            
            # Show directory structure
            print(f"\n📁 Created dataset structure:")
            for item in sorted(output_dir.rglob("*")):
                if item.is_file():
                    rel_path = item.relative_to(output_dir)
                    print(f"  {rel_path}")
            
        except Exception as e:
            print(f"\n❌ Error during dataset preparation: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # Demonstrate usage commands
        print(f"\n🎯 Next Steps (if this were a real dataset):")
        print(f"1. Review captions:")
        print(f"   python scripts/review_captions.py --dataset_dir {output_dir}")
        print(f"\n2. Start FLUX training:")
        print(f"   ./scripts/train_production.sh flux config/flux_variable_size_config.yaml --variable_size")
        print(f"\n3. Generate images after training:")
        print(f"   ./scripts/inference_flux.sh --prompt 'your prompt' --lora_path ./outputs/flux-finetuned/lora")
        
        # Keep demo files for inspection
        demo_output = Path("./demo_auto_dataset")
        if demo_output.exists():
            shutil.rmtree(demo_output)
        shutil.copytree(output_dir, demo_output)
        
        print(f"\n💾 Demo dataset saved to: {demo_output}")
        print("You can inspect the generated metadata and structure!")


def show_captioning_comparison():
    """Show comparison of different captioning models"""
    
    print("\n" + "="*60)
    print("🤖 CAPTIONING MODEL COMPARISON")
    print("="*60)
    
    try:
        from src.auto_caption import get_available_models, AutoCaptioner
        
        available_models = get_available_models()
        
        if not available_models:
            print("❌ No captioning models available for comparison")
            return
            
        print(f"Available models: {available_models}")
        
        # Create a test image
        test_image = Image.new('RGB', (512, 512), (100, 150, 200))
        draw = ImageDraw.Draw(test_image)
        
        # Add some content
        draw.rectangle([100, 100, 400, 400], fill=(255, 200, 100))
        draw.ellipse([150, 150, 350, 350], fill=(200, 100, 255))
        
        print(f"\n🖼️  Testing with a synthetic image (blue background, orange rectangle, purple circle)")
        
        # Test each available model
        for model_name in available_models:
            try:
                print(f"\n🤖 Testing {model_name.upper()}:")
                
                captioner = AutoCaptioner(model_name=model_name, batch_size=1)
                caption = captioner.caption_image(test_image)
                
                print(f"   Caption: '{caption}'")
                
                # Test enhancement
                enhanced = captioner.enhance_caption(caption, test_image)
                print(f"   Enhanced: '{enhanced}'")
                
            except Exception as e:
                print(f"   ❌ Error with {model_name}: {e}")
                
    except ImportError:
        print("❌ Auto-captioning modules not available")


def show_usage_examples():
    """Show practical usage examples"""
    
    print("\n" + "="*60)
    print("📚 USAGE EXAMPLES")
    print("="*60)
    
    examples = [
        {
            "title": "Basic Auto-Preparation",
            "description": "Prepare dataset from a folder of images",
            "command": "./scripts/prepare_flux_auto.sh /path/to/images ./data/train"
        },
        {
            "title": "High-Quality Dataset",
            "description": "Strict quality filtering and larger images",
            "command": "./scripts/prepare_flux_auto.sh /path/to/images ./data/train --min_resolution 768 --quality_threshold 0.8 --captioning_model blip2"
        },
        {
            "title": "Large Dataset Processing",
            "description": "Process many images efficiently",
            "command": "./scripts/prepare_flux_auto.sh /path/to/images ./data/train --max_images 10000 --batch_size 8 --num_workers 8"
        },
        {
            "title": "Memory-Efficient Processing",
            "description": "For systems with limited GPU memory",
            "command": "./scripts/prepare_flux_auto.sh /path/to/images ./data/train --batch_size 1 --captioning_model blip"
        },
        {
            "title": "Reference-Only Dataset",
            "description": "Create metadata without copying images",
            "command": "./scripts/prepare_flux_auto.sh /path/to/images ./data/train --no_copy"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['title']}")
        print(f"   {example['description']}")
        print(f"   Command: {example['command']}")
    
    print(f"\n💡 Tips:")
    print(f"  • Use --captioning_model blip2 for best quality (requires more GPU memory)")
    print(f"  • Use --captioning_model blip for faster processing")
    print(f"  • Use --captioning_model clip for basic descriptions")
    print(f"  • Adjust --batch_size based on your GPU memory")
    print(f"  • Use --quality_threshold to filter low-quality images")
    print(f"  • Review captions with: python scripts/review_captions.py --dataset_dir ./data/train")


def main():
    """Main demo function"""
    
    print("🎨 Welcome to the Automatic Dataset Preparation Demo!")
    print("This demo shows how to automatically prepare FLUX datasets from raw images.")
    
    # Check dependencies
    try:
        import torch
        import PIL
        print(f"✅ Core dependencies available (PyTorch: {torch.__version__})")
    except ImportError as e:
        print(f"❌ Missing core dependencies: {e}")
        print("Please install: pip install torch torchvision Pillow")
        return
    
    # Show what we'll demonstrate
    print(f"\n📋 This demo will show:")
    print(f"  1. Automatic image captioning with AI models")
    print(f"  2. Dataset quality analysis and filtering")
    print(f"  3. FLUX-ready metadata generation")
    print(f"  4. Usage examples and best practices")
    
    # Run demonstrations
    demonstrate_auto_captioning()
    show_captioning_comparison()
    show_usage_examples()
    
    print(f"\n" + "="*60)
    print("🎉 DEMO COMPLETE!")
    print("="*60)
    print("You now know how to automatically prepare FLUX datasets!")
    print("Just provide images, and the system will handle the rest:")
    print("  • Quality analysis and filtering")
    print("  • Automatic caption generation")
    print("  • Metadata creation")
    print("  • Dataset organization")
    print()
    print("🚀 Ready to prepare your own dataset:")
    print("./scripts/prepare_flux_auto.sh /your/images ./data/train")


if __name__ == "__main__":
    main()
