#!/usr/bin/env python3
"""
Variable Size Training Demo
Demonstrates the benefits and usage of variable-size image training
"""

import os
import sys
import json
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import numpy as np


def create_demo_images():
    """Create demo images with different aspect ratios"""
    demo_dir = Path("./demo_variable_size")
    images_dir = demo_dir / "images"
    images_dir.mkdir(parents=True, exist_ok=True)
    
    # Define different aspect ratios and sizes
    image_configs = [
        # Square images
        {"size": (512, 512), "color": (255, 100, 100), "name": "square_512", "caption": "A red square image"},
        {"size": (768, 768), "color": (100, 255, 100), "name": "square_768", "caption": "A green square image"},
        {"size": (1024, 1024), "color": (100, 100, 255), "name": "square_1024", "caption": "A blue square image"},
        
        # Landscape images
        {"size": (1280, 720), "color": (255, 255, 100), "name": "landscape_16_9", "caption": "A yellow landscape image in 16:9 ratio"},
        {"size": (1024, 576), "color": (255, 100, 255), "name": "landscape_16_9_small", "caption": "A magenta landscape image"},
        {"size": (960, 640), "color": (100, 255, 255), "name": "landscape_3_2", "caption": "A cyan landscape image in 3:2 ratio"},
        
        # Portrait images
        {"size": (576, 1024), "color": (200, 150, 100), "name": "portrait_9_16", "caption": "A brown portrait image in 9:16 ratio"},
        {"size": (720, 1280), "color": (150, 200, 100), "name": "portrait_9_16_large", "caption": "A olive portrait image"},
        {"size": (640, 960), "color": (100, 150, 200), "name": "portrait_2_3", "caption": "A steel blue portrait image in 2:3 ratio"},
        
        # Unusual aspect ratios
        {"size": (1200, 400), "color": (255, 200, 150), "name": "panoramic", "caption": "A panoramic image with 3:1 ratio"},
        {"size": (400, 1200), "color": (150, 255, 200), "name": "vertical_banner", "caption": "A vertical banner image with 1:3 ratio"},
    ]
    
    metadata = []
    
    for config in image_configs:
        # Create image
        img = Image.new('RGB', config["size"], config["color"])
        
        # Add text overlay
        draw = ImageDraw.Draw(img)
        
        # Try to use a font, fallback to default if not available
        try:
            font_size = min(config["size"]) // 20
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", font_size)
        except:
            try:
                font = ImageFont.load_default()
            except:
                font = None
        
        # Draw text
        text = f"{config['size'][0]}x{config['size'][1]}"
        if font:
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
        else:
            text_width, text_height = 100, 20
            
        x = (config["size"][0] - text_width) // 2
        y = (config["size"][1] - text_height) // 2
        
        # Draw background rectangle for text
        draw.rectangle([x-10, y-5, x+text_width+10, y+text_height+5], fill=(0, 0, 0, 128))
        draw.text((x, y), text, fill=(255, 255, 255), font=font)
        
        # Save image
        image_path = images_dir / f"{config['name']}.jpg"
        img.save(image_path, quality=95)
        
        # Add to metadata
        metadata.append({
            "image": f"images/{config['name']}.jpg",
            "caption": config["caption"],
            "width": config["size"][0],
            "height": config["size"][1],
            "aspect_ratio": config["size"][0] / config["size"][1]
        })
    
    # Save metadata
    metadata_path = demo_dir / "metadata.json"
    with open(metadata_path, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print(f"Created {len(image_configs)} demo images in {demo_dir}")
    print(f"Metadata saved to {metadata_path}")
    
    return demo_dir


def analyze_aspect_ratios(metadata_path):
    """Analyze aspect ratios in the dataset"""
    with open(metadata_path, 'r') as f:
        metadata = json.load(f)
    
    print("\n📊 Aspect Ratio Analysis:")
    print("=" * 50)
    
    aspect_ratios = []
    for item in metadata:
        ratio = item["aspect_ratio"]
        aspect_ratios.append(ratio)
        print(f"  {item['image']:<25} {item['width']:>4}x{item['height']:<4} ratio: {ratio:.3f}")
    
    print(f"\nTotal images: {len(metadata)}")
    print(f"Unique aspect ratios: {len(set(f'{r:.3f}' for r in aspect_ratios))}")
    print(f"Aspect ratio range: {min(aspect_ratios):.3f} - {max(aspect_ratios):.3f}")


def demonstrate_bucketing():
    """Demonstrate how bucketing works with variable sizes"""
    print("\n🪣 Bucketing Demonstration:")
    print("=" * 50)
    
    # Define bucket sizes (from config)
    bucket_sizes = [
        (512, 512), (576, 576), (640, 640), (704, 704), (768, 768),
        (832, 832), (896, 896), (960, 960), (1024, 1024),
        (512, 768), (576, 864), (640, 960), (704, 1056),
        (768, 512), (864, 576), (960, 640), (1056, 704),
        (1280, 720), (1024, 576), (960, 540), (854, 480)
    ]
    
    # Example images with their original sizes
    example_images = [
        {"name": "photo1.jpg", "size": (1920, 1080), "ratio": 1920/1080},
        {"name": "photo2.jpg", "size": (800, 1200), "ratio": 800/1200},
        {"name": "photo3.jpg", "size": (1000, 1000), "ratio": 1.0},
        {"name": "photo4.jpg", "size": (1600, 900), "ratio": 1600/900},
        {"name": "photo5.jpg", "size": (600, 800), "ratio": 600/800},
    ]
    
    print("Original images and their assigned buckets:")
    print()
    
    for img in example_images:
        # Find best bucket
        best_bucket = None
        best_diff = float('inf')
        
        for bucket_w, bucket_h in bucket_sizes:
            bucket_ratio = bucket_w / bucket_h
            diff = abs(bucket_ratio - img["ratio"])
            
            if diff < best_diff:
                best_diff = diff
                best_bucket = (bucket_w, bucket_h)
        
        print(f"  {img['name']:<12} {img['size'][0]:>4}x{img['size'][1]:<4} (ratio: {img['ratio']:.3f}) → bucket: {best_bucket[0]}x{best_bucket[1]} (ratio: {best_bucket[0]/best_bucket[1]:.3f})")
    
    print("\n💡 Benefits of bucketing:")
    print("  ✅ Groups similar aspect ratios together")
    print("  ✅ Reduces padding waste")
    print("  ✅ More efficient GPU memory usage")
    print("  ✅ Better batch utilization")


def demonstrate_resize_modes():
    """Demonstrate different resize modes"""
    print("\n🔄 Resize Mode Demonstration:")
    print("=" * 50)
    
    resize_modes = [
        {
            "mode": "pad",
            "description": "Maintains aspect ratio, adds padding",
            "pros": ["No distortion", "Preserves all content", "Best quality"],
            "cons": ["Some padding overhead", "Slightly larger memory usage"]
        },
        {
            "mode": "crop", 
            "description": "Maintains aspect ratio, crops to fit",
            "pros": ["No padding", "Efficient memory usage", "Clean edges"],
            "cons": ["May lose content", "Potential information loss"]
        },
        {
            "mode": "stretch",
            "description": "Stretches to exact dimensions",
            "pros": ["No padding or cropping", "Uses exact target size"],
            "cons": ["Distorts aspect ratio", "May affect quality"]
        }
    ]
    
    for mode in resize_modes:
        print(f"\n📐 {mode['mode'].upper()} Mode:")
        print(f"   Description: {mode['description']}")
        print(f"   Pros: {', '.join(mode['pros'])}")
        print(f"   Cons: {', '.join(mode['cons'])}")
    
    print("\n💡 Recommendation: Use 'pad' mode for best quality and content preservation")


def show_training_commands():
    """Show training commands for variable-size training"""
    print("\n🚀 Variable-Size Training Commands:")
    print("=" * 50)
    
    print("\n1. Prepare your dataset:")
    print("   # For Wan2.1 (video)")
    print("   python scripts/prepare_dataset.py --mode videos --input_dir /path/to/videos --output_dir ./data/train")
    print()
    print("   # For FLUX (images)")
    print("   python scripts/prepare_flux_dataset.py --mode text2image --input_dir /path/to/images --output_dir ./data/train")
    
    print("\n2. Train with variable sizes:")
    print("   # Wan2.1 variable-size training")
    print("   ./scripts/train_variable_size.sh wan2.1")
    print()
    print("   # FLUX variable-size training")
    print("   ./scripts/train_variable_size.sh flux")
    
    print("\n3. Direct Python commands:")
    print("   # Wan2.1")
    print("   python src/train_variable_size.py --model_type wan2.1 --config config/wan2_1_variable_size_config.yaml")
    print()
    print("   # FLUX")
    print("   python src/train_variable_size.py --model_type flux --config config/flux_variable_size_config.yaml")


def show_memory_comparison():
    """Show memory usage comparison"""
    print("\n💾 Memory Usage Comparison:")
    print("=" * 50)
    
    comparison_data = [
        {"method": "Fixed Size (Crop)", "memory": "High", "quality": "Good", "content_loss": "Yes"},
        {"method": "Fixed Size (Stretch)", "memory": "High", "quality": "Poor", "content_loss": "No"},
        {"method": "Variable Size (Pad)", "memory": "Optimal", "quality": "Excellent", "content_loss": "No"},
    ]
    
    print(f"{'Method':<25} {'Memory':<10} {'Quality':<12} {'Content Loss'}")
    print("-" * 60)
    for data in comparison_data:
        print(f"{data['method']:<25} {data['memory']:<10} {data['quality']:<12} {data['content_loss']}")
    
    print("\n💡 Variable-size training provides the best balance of memory efficiency and quality!")


def main():
    """Main demonstration function"""
    print("🎨 Variable-Size Image Training Demonstration")
    print("=" * 60)
    print("This demo shows the benefits of training with variable-sized images")
    print("without cropping, maintaining aspect ratios and preserving content.")
    print()
    
    # Create demo dataset
    demo_dir = create_demo_images()
    
    # Analyze the dataset
    analyze_aspect_ratios(demo_dir / "metadata.json")
    
    # Demonstrate bucketing
    demonstrate_bucketing()
    
    # Demonstrate resize modes
    demonstrate_resize_modes()
    
    # Show memory comparison
    show_memory_comparison()
    
    # Show training commands
    show_training_commands()
    
    print("\n" + "=" * 60)
    print("🎉 Variable-Size Training Benefits Summary:")
    print("✅ No content loss from cropping")
    print("✅ Preserves original aspect ratios")
    print("✅ Efficient memory usage with bucketing")
    print("✅ Better training quality and diversity")
    print("✅ Works with any image size or aspect ratio")
    print()
    print("📁 Demo dataset created at:", demo_dir)
    print("🚀 Ready to try variable-size training!")
    print()
    print("Next steps:")
    print("1. Use your own dataset with mixed aspect ratios")
    print("2. Run: ./scripts/train_variable_size.sh [wan2.1|flux]")
    print("3. Enjoy better quality training without cropping!")


if __name__ == "__main__":
    main()
