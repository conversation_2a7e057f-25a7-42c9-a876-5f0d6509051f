# 🏭 Production Deployment Guide

This guide covers deploying the diffusion model training suite in production environments with proper security, monitoring, and scalability.

## 🚀 Quick Production Setup

### 1. Automated Setup
```bash
# Clone and setup
git clone <repository>
cd finetune

# Run automated production setup
chmod +x scripts/setup_production.sh
./scripts/setup_production.sh
```

### 2. Configure Environment
```bash
# Copy and edit environment file
cp .env.example .env
nano .env  # Edit with your actual values
```

### 3. Start Training
```bash
# Production training with full monitoring
./scripts/train_production.sh wan2.1 config/wan2_1_config.yaml
./scripts/train_production.sh flux config/flux_lora_config.yaml --variable_size
```

## 🔧 Environment Configuration

### Required Environment Variables

#### Hugging Face Authentication
```bash
# Get token from: https://huggingface.co/settings/tokens
HF_TOKEN=hf_your_actual_token_here
HF_HOME=/path/to/hf/cache  # Optional
```

#### Weights & Biases (Optional)
```bash
# Get key from: https://wandb.ai/authorize
WANDB_API_KEY=your_wandb_key_here
WANDB_PROJECT=your_project_name
WANDB_ENTITY=your_username_or_team
```

#### Hardware Configuration
```bash
CUDA_VISIBLE_DEVICES=0,1  # GPU devices to use
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
MIXED_PRECISION=bf16  # fp16, bf16, or no
```

#### Storage Paths
```bash
OUTPUT_DIR=./outputs
LOGGING_DIR=./logs
CACHE_DIR=./cache
DATA_DIR=./data
```

### Security Best Practices

#### 1. Token Management
- ✅ Use `.env` files for sensitive data
- ✅ Never commit `.env` to version control
- ✅ Use different tokens for dev/staging/prod
- ✅ Rotate tokens regularly

#### 2. Access Control
```bash
# Set restrictive permissions
chmod 600 .env
chmod 700 logs/ outputs/ cache/

# Use service accounts in production
HF_TOKEN=hf_service_account_token
```

#### 3. Network Security
```bash
# Restrict outbound connections if needed
# Use VPN or private networks for sensitive models
# Monitor API usage and rate limits
```

## 📊 Production Monitoring

### System Monitoring
The production suite includes comprehensive monitoring:

```python
# Automatic system monitoring
- CPU, Memory, Disk usage
- GPU memory and utilization
- Training metrics and progress
- Error detection and alerts
```

### Logging Levels
```bash
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR
VERBOSE_LOGGING=false  # Detailed logs
```

### Notification Setup

#### Slack Notifications
```bash
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
```

#### Email Notifications
```bash
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
NOTIFICATION_EMAIL=<EMAIL>
```

## 🔄 CI/CD Integration

### GitHub Actions Example
```yaml
name: Model Training
on:
  push:
    branches: [main]
    paths: ['data/**', 'config/**']

jobs:
  train:
    runs-on: self-hosted  # GPU runner
    steps:
      - uses: actions/checkout@v4
      - name: Setup Environment
        run: ./scripts/setup_production.sh
      - name: Train Model
        env:
          HF_TOKEN: ${{ secrets.HF_TOKEN }}
          WANDB_API_KEY: ${{ secrets.WANDB_API_KEY }}
        run: ./scripts/train_production.sh flux config/flux_lora_config.yaml
```

### Docker Deployment
```dockerfile
FROM nvidia/cuda:12.1-devel-ubuntu22.04

# Install Python and dependencies
RUN apt-get update && apt-get install -y python3 python3-pip
COPY requirements-production.txt .
RUN pip install -r requirements-production.txt

# Copy application
COPY . /app
WORKDIR /app

# Setup production environment
RUN ./scripts/setup_production.sh

# Run training
CMD ["./scripts/train_production.sh", "flux", "config/flux_lora_config.yaml"]
```

## 📈 Scaling and Performance

### Multi-GPU Training
```bash
# Use multiple GPUs
CUDA_VISIBLE_DEVICES=0,1,2,3
accelerate config  # Configure multi-GPU setup

# Launch distributed training
accelerate launch src/train_production.py --config config/flux_lora_config.yaml
```

### Memory Optimization
```bash
# For limited GPU memory
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:256
USE_8BIT_ADAM=true
GRADIENT_CHECKPOINTING=true

# Use QLoRA for extreme memory efficiency
./scripts/train_production.sh flux config/flux_qlora_config.yaml
```

### Cloud Deployment

#### AWS Setup
```bash
# S3 for data storage
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
S3_BUCKET=your-training-bucket

# EC2 instance recommendations
# p3.2xlarge: 1x V100 (16GB) - Good for development
# p3.8xlarge: 4x V100 (64GB) - Production training
# p4d.24xlarge: 8x A100 (320GB) - Large-scale training
```

#### Google Cloud Setup
```bash
# GCS for data storage
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json
GCS_BUCKET=your-training-bucket

# Compute Engine recommendations
# n1-standard-8 + 1x T4 - Development
# n1-standard-16 + 1x V100 - Production
# a2-highgpu-8g + 8x A100 - Large-scale
```

## 🛡️ Security Hardening

### Production Checklist
- [ ] Environment variables properly configured
- [ ] Sensitive data not in version control
- [ ] Access tokens rotated regularly
- [ ] Monitoring and alerting enabled
- [ ] Backup strategy implemented
- [ ] Disaster recovery plan tested

### Secrets Management
```bash
# Use external secret management
# AWS Secrets Manager
# Azure Key Vault
# Google Secret Manager
# HashiCorp Vault

# Example with AWS
aws secretsmanager get-secret-value --secret-id prod/hf-token
```

### Network Security
```bash
# Firewall rules
# Only allow necessary outbound connections
# Block unnecessary ports
# Use VPN for sensitive operations

# API rate limiting
# Monitor API usage
# Implement backoff strategies
# Use multiple tokens if needed
```

## 📋 Operational Procedures

### Daily Operations
```bash
# Check system health
./scripts/health_check.sh

# Monitor training progress
tail -f logs/training_session_*.log

# Check GPU utilization
nvidia-smi -l 1
```

### Backup Procedures
```bash
# Backup models
rsync -av outputs/ backup/models/$(date +%Y%m%d)/

# Backup configurations
tar -czf backup/configs_$(date +%Y%m%d).tar.gz config/

# Backup logs
tar -czf backup/logs_$(date +%Y%m%d).tar.gz logs/
```

### Disaster Recovery
```bash
# Model recovery
aws s3 sync s3://backup-bucket/models/ outputs/

# Configuration recovery
tar -xzf backup/configs_latest.tar.gz

# Resume training
./scripts/train_production.sh flux config/flux_lora_config.yaml --resume_from_checkpoint outputs/checkpoint-1000
```

## 🔍 Troubleshooting

### Common Issues

#### CUDA Out of Memory
```bash
# Solutions:
1. Reduce batch size
2. Enable gradient checkpointing
3. Use QLoRA configuration
4. Increase gradient accumulation steps
```

#### Model Access Denied
```bash
# Solutions:
1. Check HF_TOKEN validity
2. Accept model license on Hugging Face
3. Verify model permissions
4. Use correct model path
```

#### Slow Training
```bash
# Optimizations:
1. Enable xformers memory efficient attention
2. Use mixed precision training
3. Optimize data loading (more workers)
4. Use faster storage (NVMe SSD)
```

### Debug Mode
```bash
# Enable debug logging
LOG_LEVEL=DEBUG
VERBOSE_LOGGING=true

# Run with profiling
ENABLE_PROFILING=true
python -m cProfile -o profile.stats src/train_production.py
```

## 📞 Support and Maintenance

### Health Monitoring
```bash
# System health check
python -c "
from src.production_utils import SystemMonitor
monitor = SystemMonitor()
stats = monitor.get_system_stats()
print(f'GPU Memory: {stats[\"gpu\"][0][\"utilization_percent\"]:.1f}%')
print(f'System Memory: {stats[\"memory\"][\"percent\"]:.1f}%')
"
```

### Log Analysis
```bash
# Find errors in logs
grep -i error logs/*.log

# Monitor training progress
grep "Step" logs/training_session_*.log | tail -20

# Check GPU memory usage
grep "GPU" logs/*.log | tail -10
```

### Performance Tuning
```bash
# Profile training
python -m torch.profiler src/train_production.py

# Memory analysis
python -m memory_profiler src/train_production.py

# GPU profiling
nsys profile python src/train_production.py
```

## 🎯 Production Best Practices

### 1. Environment Management
- Use virtual environments or containers
- Pin dependency versions
- Test in staging before production
- Maintain environment documentation

### 2. Configuration Management
- Use version-controlled configurations
- Separate configs for different environments
- Validate configurations before training
- Document configuration changes

### 3. Data Management
- Implement data versioning
- Use checksums for data integrity
- Backup training data regularly
- Monitor data quality

### 4. Model Management
- Version control model artifacts
- Implement model testing pipelines
- Use model registries
- Document model lineage

### 5. Monitoring and Alerting
- Monitor system resources continuously
- Set up alerts for failures
- Track training metrics
- Implement health checks

This production guide ensures your diffusion model training runs reliably, securely, and efficiently in production environments! 🚀
