# 🎨 FLUX.1-dev Lo<PERSON>/QLoRA Training Guide

Complete guide for finetuning FLUX.1-dev text-to-image model using LoRA and QLoRA techniques.

## 🚀 Quick Start

### 1. Choose Your Training Type

**Style LoRA** (Recommended for beginners):
```bash
# Prepare style dataset
python scripts/prepare_flux_dataset.py \
    --mode style \
    --input_dir /path/to/style/images \
    --output_dir ./data/flux_style \
    --style_name "MY<PERSON>Y<PERSON>" \
    --resize_images

# Train style LoRA
./scripts/train_flux.sh lora
```

**Dreambooth** (For specific subjects):
```bash
# Prepare dreambooth dataset
python scripts/prepare_flux_dataset.py \
    --mode dreambooth \
    --input_dir /path/to/subject/images \
    --output_dir ./data/flux_dreambooth \
    --instance_prompt "a photo of sks person" \
    --resize_images

# Train dreambooth LoRA
./scripts/train_flux.sh lora
```

**Text-to-Image** (General purpose):
```bash
# Prepare text-to-image dataset
python scripts/prepare_flux_dataset.py \
    --mode text2image \
    --input_dir /path/to/images \
    --output_dir ./data/flux_text2image \
    --resize_images

# Train text-to-image LoRA
./scripts/train_flux.sh lora
```

### 2. Generate Images

```bash
# Basic generation
./scripts/inference_flux.sh --prompt "A beautiful landscape painting"

# With your trained LoRA
./scripts/inference_flux.sh \
    --prompt "A portrait in MYSTYLE style" \
    --lora_path ./outputs/flux-lora-finetuned/lora
```

## 📋 Prerequisites

### Hardware Requirements

| GPU Memory | Recommended Method | Resolution | Batch Size |
|------------|-------------------|------------|------------|
| 12-16GB | QLoRA | 768x768 | 1 |
| 16-24GB | LoRA or QLoRA | 1024x1024 | 1 |
| 24GB+ | LoRA | 1024x1024 | 1-2 |

### Software Requirements

```bash
# Install dependencies
pip install torch>=2.4.0 torchvision>=0.19.0
pip install diffusers>=0.31.0 transformers>=4.40.0
pip install accelerate>=0.28.0 peft>=0.10.0
pip install bitsandbytes>=0.43.0  # For QLoRA
pip install wandb  # For monitoring
```

## 🎯 Training Methods Comparison

### LoRA vs QLoRA for FLUX

| Method | Memory Usage | Quality | Speed | Best For |
|--------|-------------|---------|-------|----------|
| **LoRA** | High (20-24GB) | Excellent | Fast | High-end GPUs |
| **QLoRA** | Low (12-16GB) | Very Good | Moderate | Consumer GPUs |

### Training Types

| Type | Use Case | Dataset Size | Training Time |
|------|----------|--------------|---------------|
| **Style LoRA** | Artistic styles | 20-100 images | 1-3 hours |
| **Dreambooth** | Specific subjects | 5-20 images | 30min-2 hours |
| **Text-to-Image** | General concepts | 100+ images | 2-8 hours |

## 📁 Dataset Preparation

### Style LoRA Dataset

Best for learning artistic styles, photography styles, or visual aesthetics.

```bash
# Prepare style dataset
python scripts/prepare_flux_dataset.py \
    --mode style \
    --input_dir ./my_style_images \
    --output_dir ./data/flux_style \
    --style_name "CYBERPUNK" \
    --style_description "cyberpunk digital art" \
    --resize_images \
    --target_size 1024
```

**Dataset Structure:**
```
data/flux_style/
├── metadata.json
└── images/
    ├── style_000000.jpg
    ├── style_000001.jpg
    └── ...
```

**Example metadata.json:**
```json
[
  {
    "image": "images/style_000000.jpg",
    "caption": "A cyberpunk digital art image of a futuristic city in CYBERPUNK style",
    "style_name": "CYBERPUNK"
  }
]
```

### Dreambooth Dataset

Best for learning specific subjects (people, objects, characters).

```bash
# Prepare dreambooth dataset
python scripts/prepare_flux_dataset.py \
    --mode dreambooth \
    --input_dir ./subject_photos \
    --output_dir ./data/flux_dreambooth \
    --instance_prompt "a photo of sks person" \
    --class_prompt "a photo of person" \
    --resize_images
```

**Tips for Dreambooth:**
- Use 5-20 high-quality images of your subject
- Vary poses, lighting, and backgrounds
- Choose a unique identifier (e.g., "sks", "xyz123")
- Keep consistent image quality

### Text-to-Image Dataset

Best for general concept learning or large-scale training.

```bash
# Prepare text-to-image dataset
python scripts/prepare_flux_dataset.py \
    --mode text2image \
    --input_dir ./diverse_images \
    --output_dir ./data/flux_text2image \
    --resize_images \
    --min_size 512
```

## ⚙️ Configuration

### LoRA Configuration (config/flux_lora_config.yaml)

```yaml
model:
  lora_rank: 16          # Start with 16, increase for complex concepts
  lora_alpha: 16         # Usually same as rank
  lora_dropout: 0.1      # Prevent overfitting

training:
  resolution: 1024       # FLUX native resolution
  learning_rate: 2e-4    # Higher LR for LoRA
  max_train_steps: 4000  # Adjust based on dataset size
  guidance_scale: 3.5    # FLUX works well with lower guidance
```

### QLoRA Configuration (config/flux_qlora_config.yaml)

```yaml
model:
  use_4bit: true         # Enable 4-bit quantization
  lora_rank: 16          # Lower rank for QLoRA
  bnb_4bit_quant_type: "nf4"  # NormalFloat4 quantization

training:
  resolution: 768        # Reduced for memory efficiency
  learning_rate: 3e-4    # Higher LR for QLoRA
  gradient_accumulation_steps: 8  # Compensate for smaller batch
```

## 🚀 Training Commands

### Standard LoRA Training

```bash
# Style LoRA
./scripts/train_flux.sh lora

# With custom config
python src/train_flux.py --config config/flux_lora_config.yaml

# With overrides
python src/train_flux.py \
    --config config/flux_lora_config.yaml \
    --output_dir ./my_flux_model \
    --mixed_precision bf16
```

### QLoRA Training (Memory Efficient)

```bash
# QLoRA training
./scripts/train_flux.sh qlora

# Direct command
python src/train_flux.py --config config/flux_qlora_config.yaml
```

### Training Monitoring

```bash
# Monitor GPU usage
watch -n 1 nvidia-smi

# Check training logs
tail -f logs/training.log

# Weights & Biases (if configured)
wandb login
# View at wandb.ai
```

## 🎨 Inference & Generation

### Basic Generation

```bash
# Simple generation
python src/inference_flux.py \
    --prompt "A beautiful sunset over mountains" \
    --output_path sunset.png

# With LoRA
python src/inference_flux.py \
    --prompt "A portrait in MYSTYLE style" \
    --lora_path ./outputs/flux-lora-finetuned/lora \
    --output_path portrait.png
```

### Advanced Generation

```bash
# High quality settings
python src/inference_flux.py \
    --prompt "A detailed cyberpunk cityscape" \
    --lora_path ./outputs/flux-lora-finetuned/lora \
    --width 1024 \
    --height 1024 \
    --guidance_scale 5.0 \
    --num_inference_steps 50 \
    --seed 42 \
    --output_path cyberpunk_city.png
```

### Batch Generation

```bash
# Create prompts file
echo "A portrait in MYSTYLE style" > prompts.txt
echo "A landscape in MYSTYLE style" >> prompts.txt
echo "A still life in MYSTYLE style" >> prompts.txt

# Batch generate
python src/inference_flux.py \
    --batch_prompts prompts.txt \
    --lora_path ./outputs/flux-lora-finetuned/lora \
    --output_dir ./batch_outputs
```

## 🔧 Troubleshooting

### CUDA Out of Memory

```bash
# Switch to QLoRA
./scripts/train_flux.sh qlora

# Reduce resolution
# Edit config: resolution: 768 (or 512)

# Increase gradient accumulation
# Edit config: gradient_accumulation_steps: 8
```

### Poor Quality Results

```bash
# Increase LoRA rank
# Edit config: lora_rank: 32 (or 64)

# More training steps
# Edit config: max_train_steps: 8000

# Better dataset
# Ensure high-quality, diverse images
# Write detailed, accurate captions
```

### Training Too Slow

```bash
# Enable optimizations
# Edit config:
# enable_xformers_memory_efficient_attention: true
# pin_memory: true
# dataloader_num_workers: 4
```

## 📊 Expected Results

### Training Times (RTX 4090)

- **Style LoRA (50 images)**: 1-2 hours
- **Dreambooth (10 images)**: 30-60 minutes  
- **Text-to-Image (500 images)**: 4-6 hours

### Memory Usage

- **LoRA**: 18-22GB VRAM
- **QLoRA**: 12-16GB VRAM

### Quality Expectations

- **Style LoRA**: Excellent style transfer, maintains FLUX quality
- **Dreambooth**: High subject fidelity, good generalization
- **Text-to-Image**: Broad concept learning, versatile generation

## 🎯 Best Practices

### Dataset Quality

1. **High Resolution**: Use 1024x1024 or higher source images
2. **Diverse Composition**: Vary angles, lighting, backgrounds
3. **Consistent Quality**: Remove blurry or low-quality images
4. **Good Captions**: Write descriptive, accurate captions

### Training Strategy

1. **Start Small**: Begin with 16-32 rank, increase if needed
2. **Monitor Overfitting**: Use validation images
3. **Experiment with LR**: Try 1e-4 to 5e-4 range
4. **Save Checkpoints**: Regular checkpointing for recovery

### Inference Tips

1. **Prompt Engineering**: Be specific and descriptive
2. **Guidance Scale**: Start with 3.5, adjust as needed
3. **Steps**: 28 steps usually sufficient, 50 for high quality
4. **Seed Control**: Use seeds for reproducible results

## 🔗 Advanced Techniques

### Multi-Concept Training

Train on multiple concepts simultaneously:

```json
{
  "image": "images/image_001.jpg",
  "caption": "A STYLE1 painting of a CONCEPT1 in a forest"
}
```

### Progressive Training

Start with low resolution, increase gradually:

1. Train at 512x512 for 1000 steps
2. Continue at 768x768 for 1000 steps  
3. Finish at 1024x1024 for 2000 steps

### Ensemble LoRAs

Combine multiple LoRAs for complex effects:

```python
# Load multiple LoRAs
pipe.load_lora_weights("style_lora", adapter_name="style")
pipe.load_lora_weights("concept_lora", adapter_name="concept")
pipe.set_adapters(["style", "concept"], adapter_weights=[0.8, 0.6])
```

This comprehensive guide covers everything you need to successfully finetune FLUX.1-dev with LoRA and QLoRA techniques! 🎉
