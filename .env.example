# Production Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# HUGGING FACE CONFIGURATION
# =============================================================================

# Hugging Face Hub Token (required for model downloads and uploads)
# Get your token from: https://huggingface.co/settings/tokens
HF_TOKEN=your_huggingface_token_here

# Hugging Face Hub Cache Directory (optional)
# Default: ~/.cache/huggingface/hub
HF_HOME=/path/to/your/hf_cache

# Hugging Face Hub Offline Mode (optional)
# Set to true to use only cached models
HF_HUB_OFFLINE=false

# =============================================================================
# WEIGHTS & BIASES CONFIGURATION
# =============================================================================

# Weights & Biases API Key (optional, for experiment tracking)
# Get your key from: https://wandb.ai/authorize
WANDB_API_KEY=your_wandb_api_key_here

# Weights & Biases Project Name (optional)
WANDB_PROJECT=diffusion-finetuning

# Weights & Biases Entity/Team Name (optional)
WANDB_ENTITY=your_username_or_team

# Weights & Biases Mode (optional)
# Options: online, offline, disabled
WANDB_MODE=online

# Weights & Biases Run Name Prefix (optional)
WANDB_RUN_PREFIX=experiment

# =============================================================================
# TRAINING CONFIGURATION
# =============================================================================

# Default Output Directory
OUTPUT_DIR=./outputs

# Default Logging Directory
LOGGING_DIR=./logs

# Default Cache Directory
CACHE_DIR=./cache

# Default Data Directory
DATA_DIR=./data

# =============================================================================
# HARDWARE CONFIGURATION
# =============================================================================

# CUDA Visible Devices (optional)
# Example: "0" for single GPU, "0,1" for multi-GPU
CUDA_VISIBLE_DEVICES=0

# PyTorch CUDA Memory Allocation Configuration
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# Tokenizers Parallelism (recommended: false for training)
TOKENIZERS_PARALLELISM=false

# =============================================================================
# MODEL CONFIGURATION
# =============================================================================

# Default Model Paths (optional, override config defaults)
WAN2_1_MODEL_PATH=Wan-AI/Wan2.1-I2V-14B-720P-Diffusers
FLUX_MODEL_PATH=black-forest-labs/FLUX.1-dev

# Model Revision (optional)
MODEL_REVISION=main

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Trust Remote Code (security setting)
# Only set to true if you trust the model source
TRUST_REMOTE_CODE=false

# Use Auth Token for Private Models
USE_AUTH_TOKEN=true

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Number of DataLoader Workers
NUM_WORKERS=4

# Pin Memory for DataLoader
PIN_MEMORY=true

# Mixed Precision Training
# Options: no, fp16, bf16
MIXED_PRECISION=bf16

# Enable TensorFloat-32 (TF32) on Ampere GPUs
ALLOW_TF32=true

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Enable Detailed Logging
VERBOSE_LOGGING=false

# Log Level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Save Intermediate Checkpoints
SAVE_INTERMEDIATE_CHECKPOINTS=true

# Checkpoint Save Frequency (steps)
CHECKPOINT_SAVE_STEPS=500

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Development Mode (enables additional debugging)
DEV_MODE=false

# Disable Warnings (for cleaner output)
DISABLE_WARNINGS=false

# Enable Profiling
ENABLE_PROFILING=false

# =============================================================================
# CLOUD CONFIGURATION (Optional)
# =============================================================================

# AWS Configuration (if using S3 for storage)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_DEFAULT_REGION=us-east-1
S3_BUCKET=your-training-bucket

# Google Cloud Configuration (if using GCS)
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json
GCS_BUCKET=your-training-bucket

# Azure Configuration (if using Azure Blob Storage)
AZURE_STORAGE_CONNECTION_STRING=your_connection_string
AZURE_CONTAINER_NAME=your-container

# =============================================================================
# NOTIFICATION CONFIGURATION (Optional)
# =============================================================================

# Slack Webhook for Training Notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# Discord Webhook for Training Notifications
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/YOUR/DISCORD/WEBHOOK

# Email Configuration for Notifications
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
NOTIFICATION_EMAIL=<EMAIL>

# =============================================================================
# ADVANCED CONFIGURATION
# =============================================================================

# Custom Python Path (if needed)
PYTHONPATH=/path/to/custom/modules

# Custom LD_LIBRARY_PATH (if needed)
LD_LIBRARY_PATH=/path/to/custom/libs

# OMP Number of Threads
OMP_NUM_THREADS=1

# MKL Number of Threads
MKL_NUM_THREADS=1

# =============================================================================
# EXPERIMENT TRACKING
# =============================================================================

# MLflow Tracking URI (alternative to W&B)
MLFLOW_TRACKING_URI=http://localhost:5000

# TensorBoard Log Directory
TENSORBOARD_LOG_DIR=./tensorboard_logs

# =============================================================================
# DATASET CONFIGURATION
# =============================================================================

# Maximum Dataset Size (for development/testing)
MAX_DATASET_SIZE=1000

# Dataset Preprocessing Workers
PREPROCESSING_WORKERS=8

# Enable Dataset Caching
ENABLE_DATASET_CACHE=true

# Dataset Cache Directory
DATASET_CACHE_DIR=./dataset_cache
