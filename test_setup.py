#!/usr/bin/env python3
"""
Test script to verify the Wan2.1 finetuning setup
"""

import sys
import importlib
from pathlib import Path

def test_imports():
    """Test if all required packages can be imported"""
    print("Testing package imports...")
    
    required_packages = [
        "torch",
        "torchvision", 
        "diffusers",
        "transformers",
        "accelerate",
        "datasets",
        "PIL",
        "cv2",
        "numpy",
        "tqdm",
        "omegaconf",
        "einops",
        "peft",
        "bitsandbytes",
        "wandb",
    ]
    
    failed_imports = []
    
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✅ {package}")
        except ImportError as e:
            print(f"❌ {package}: {e}")
            failed_imports.append(package)
    
    if failed_imports:
        print(f"\n⚠️  Failed to import: {', '.join(failed_imports)}")
        print("Please install missing packages with: pip install -r requirements.txt")
        return False
    else:
        print("\n✅ All required packages imported successfully!")
        return True


def test_model_access():
    """Test if we can access the Wan2.1 model"""
    print("\nTesting model access...")
    
    try:
        from diffusers import WanImageToVideoPipeline
        from transformers import CLIPVisionModel
        
        # Try to load model info (without downloading)
        model_id = "Wan-AI/Wan2.1-I2V-14B-720P-Diffusers"
        
        print(f"✅ Can import WanImageToVideoPipeline")
        print(f"✅ Model ID: {model_id}")
        print("Note: Model will be downloaded during first training/inference")
        
        return True
        
    except Exception as e:
        print(f"❌ Error accessing model: {e}")
        return False


def test_dataset_loading():
    """Test dataset loading functionality"""
    print("\nTesting dataset functionality...")
    
    try:
        sys.path.append("src")
        from dataset import ImageVideoDataset, create_metadata_template
        
        print("✅ Can import dataset classes")
        
        # Test metadata template creation
        test_dir = Path("./test_data")
        if test_dir.exists():
            print("✅ Dataset utilities work")
        else:
            print("ℹ️  No test data found (this is normal)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error with dataset: {e}")
        return False


def test_config_files():
    """Test if configuration files exist and are valid"""
    print("\nTesting configuration files...")
    
    config_files = [
        "config/wan2_1_config.yaml",
        "requirements.txt",
        "README.md",
    ]
    
    all_exist = True
    
    for config_file in config_files:
        if Path(config_file).exists():
            print(f"✅ {config_file}")
        else:
            print(f"❌ {config_file} not found")
            all_exist = False
    
    # Test YAML loading
    try:
        from omegaconf import OmegaConf
        config = OmegaConf.load("config/wan2_1_config.yaml")
        print("✅ Configuration file is valid YAML")
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        all_exist = False
    
    return all_exist


def test_scripts():
    """Test if scripts exist and are executable"""
    print("\nTesting scripts...")
    
    scripts = [
        "scripts/train.sh",
        "scripts/inference.sh", 
        "scripts/prepare_dataset.py",
        "src/train_wan2_1.py",
        "src/inference.py",
        "src/dataset.py",
    ]
    
    all_exist = True
    
    for script in scripts:
        script_path = Path(script)
        if script_path.exists():
            print(f"✅ {script}")
        else:
            print(f"❌ {script} not found")
            all_exist = False
    
    return all_exist


def test_gpu_availability():
    """Test GPU availability"""
    print("\nTesting GPU availability...")
    
    try:
        import torch
        
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
            
            print(f"✅ CUDA available")
            print(f"✅ GPU count: {gpu_count}")
            print(f"✅ GPU 0: {gpu_name}")
            print(f"✅ GPU 0 memory: {gpu_memory:.1f} GB")
            
            if gpu_memory < 20:
                print("⚠️  Warning: GPU memory < 20GB. Consider using LoRA and memory optimizations.")
            
            return True
        else:
            print("❌ CUDA not available")
            print("⚠️  Training will be very slow on CPU")
            return False
            
    except Exception as e:
        print(f"❌ Error checking GPU: {e}")
        return False


def main():
    """Run all tests"""
    print("Wan2.1 Finetuning Setup Test")
    print("=" * 50)
    
    tests = [
        ("Package Imports", test_imports),
        ("Model Access", test_model_access),
        ("Dataset Loading", test_dataset_loading),
        ("Configuration Files", test_config_files),
        ("Scripts", test_scripts),
        ("GPU Availability", test_gpu_availability),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print("\n" + "=" * 50)
    print("SETUP TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 All tests passed! Your setup is ready for Wan2.1 finetuning.")
        print("\nNext steps:")
        print("1. Prepare your dataset: python scripts/prepare_dataset.py --help")
        print("2. Edit captions in metadata.json")
        print("3. Start training: ./scripts/train.sh")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix the issues above.")
        
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
